<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Test</title>
</head>
<body>
    <h1>Typing Indicator Test</h1>
    <div id="test-results"></div>
    
    <script>
        // Test the typing functionality
        function testTypingIndicator() {
            const results = document.getElementById('test-results');
            
            // Test 1: Check if typing message includes sender information
            const typingMessage = {
                type: "typing",
                sender: "TestUser",
                sender_id: 123,
                receiver: 456
            };
            
            // Test 2: Check if display logic works correctly
            const testData = {
                type: "typing",
                sender: "TestUser"
            };
            
            const displayText = `${testData.sender || 'Someone'} is typing...`;
            
            results.innerHTML = `
                <h2>Test Results:</h2>
                <p><strong>Test 1 - Typing Message Structure:</strong></p>
                <pre>${JSON.stringify(typingMessage, null, 2)}</pre>
                
                <p><strong>Test 2 - Display Text:</strong></p>
                <p>${displayText}</p>
                
                <p><strong>Expected:</strong> "TestUser is typing..."</p>
                <p><strong>Status:</strong> ${displayText === "TestUser is typing..." ? "✅ PASS" : "❌ FAIL"}</p>
            `;
        }
        
        // Run test when page loads
        window.onload = testTypingIndicator;
    </script>
</body>
</html>
