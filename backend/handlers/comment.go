package handlers

import (
	"encoding/json"
	"html"
	"net/http"

	"forum/logging"
	"forum/middleware"
	"forum/models"
	"forum/queries"
	"forum/utils"
)

// CreateCommentHandler handles the creation of new comments.
// It expects a POST request with a JSON body containing the comment content and post ID.
// The user must be authenticated. It sanitizes the comment content, creates the comment
// in the database, and returns the updated post information.
func CreateCommentHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.ErrorMessage(w, "Hey! Method not allowed...", http.StatusMethodNotAllowed)
		return
	}

	userID, ok := middleware.GetUserID(r)
	if !ok {
		utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
		return
	}

	var comment models.Comment
	if err := ParseJSONBody(r.Body, &comment); err != nil {
		logging.Log("[ERROR] Error parsing JSON: %v", err)
		utils.ErrorMessage(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	comment.Content = html.EscapeString(comment.Content)

	if comment.Content == "" {
		utils.ErrorMessage(w, "Comment cannot be empty", http.StatusBadRequest)
		return
	}

	comment.UserID = userID
	if err := queries.CreateComment(comment); err != nil {
		logging.Log("[ERROR] Failed to create comment: %v", err)
		utils.ErrorMessage(w, "Oops! Failed to create comment", http.StatusInternalServerError)
		return
	}

	post, err := queries.GetPostByID(comment.PostID)
	if err != nil {

		logging.Log("[ERROR] Failed to retrieve post after comment creation: %v", err)
		utils.ErrorMessage(w, "Comment created but failed to retrieve updated post", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(post)
}

// CreateCommentLikeHandler handles the liking/disliking of comments.
// It expects a POST request with a JSON body containing the comment ID and whether it's a like or dislike.
// The user must be authenticated. It updates the like/dislike status in the database.
func CreateCommentLikeHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.ErrorMessage(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	userID, ok := middleware.GetUserID(r)
	if !ok {
		utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
		return
	}

	var like models.LikeDislike
	if err := ParseJSONBody(r.Body, &like); err != nil {
		logging.Log("[ERROR] Error parsing JSON: %v", err)
		utils.ErrorMessage(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	like.UserID = userID

	if like.CommentID == 0 {
		logging.Log("[ERROR] Missing comment_id in request")
		utils.ErrorMessage(w, "Missing comment ID", http.StatusBadRequest)
		return
	}

	if err := queries.CreateCommentLikeDislike(like); err != nil {
		logging.Log("[ERROR] Error updating comment likes: %v", err)
		utils.ErrorMessage(w, "Sorry! We couldn't update your reaction. Try again.", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Comment like updated successfully"})
}
