package handlers

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"forum/logging"
	"forum/models"
	"forum/utils"
)

// ValidatePost checks if a given Post model has a non-empty title and content.
// Returns an error if either field is empty, otherwise returns nil.
func ValidatePost(p models.Post) error {
	if p.Title == "" || p.Content == "" {
		return errors.New("title and content are required")
	}
	return nil
}

// ParseJSONBody decodes a JSON request body into the provided model interface.
// It takes an io.Reader (typically r.Body from an http.Request) and a pointer to the target struct.
// Returns an error if JSON decoding fails.
func ParseJSONBody(r io.Reader, model any) error {
	return json.NewDecoder(r).Decode(model)
}

// serveTemplate serves an HTML template from the frontend/templates directory.
// It constructs the full path to the template, checks if it exists, and then serves the file.
// If the template is not found, it logs an error and sends a 404 Not Found response.
func serveTemplate(w http.ResponseWriter, r *http.Request, templatePath string) {
	path := filepath.Join("../frontend/templates", templatePath)
	_, err := os.Stat(path)
	if err != nil {
		logging.Log("[ERROR] :Error serving template: %v", err)
		w.WriteHeader(http.StatusNotFound)
		utils.ErrorMessage(w, "Template not found", http.StatusNotFound)
		return
	}
	http.ServeFile(w, r, path)
}
