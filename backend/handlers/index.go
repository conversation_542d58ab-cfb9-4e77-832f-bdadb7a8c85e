package handlers

import (
	"net/http"
	"os"

	"forum/logging"
	"forum/utils"
)

// Index handles requests to the root URL and serves the main index.html template.
// It expects a GET request. If the URL path is not "/", it returns a 404 Not Found error.
func Index(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		utils.ErrorMessage(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}
	if r.URL.Path == "/" {
		serveTemplate(w, r, "index.html")
		return
	}
	w.Header().Set("X-Status-Code", "404")
	serveTemplate(w, r, "index.html")

}

// Static serves static files from the frontend directory.
// It expects a GET request. It constructs the file path based on the request URL,
// checks if the file exists and is not a directory, and then serves the file.
// Returns 404 Not Found if the file does not exist or is a directory.
func Static(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		utils.ErrorMessage(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	path := "../frontend" + r.URL.Path
	f, err := os.Stat(path)
	if err != nil {
		logging.Log("[ERROR] %v", err)
		utils.ErrorMessage(w, "404 Not Found", http.StatusNotFound)
		return
	}
	if f.IsDir() {
		utils.ErrorMessage(w, "404 Not Found", http.StatusNotFound)
		return
	}
	http.ServeFile(w, r, path)
}
