package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"sync"

	"forum/database"
	"forum/logging"
	"forum/middleware"
	"forum/models"
	"forum/utils"
	"time"

	"github.com/gorilla/websocket"
)

var (
	messages      []models.Message
	messagesMutex sync.Mutex
	upgrader      = websocket.Upgrader{}
	clients       = make(map[*websocket.Conn]bool)
	broadcast     = make(chan models.Message)
)

// init initializes the message handling system by starting a goroutine
// that processes and broadcasts messages to connected WebSocket clients.
func init() {
	go handleMessages()
}

// handleMessages is a goroutine that continuously processes messages from the 'broadcast' channel.
// It logs and stores messages of type "message" or empty type, then sends them to all connected WebSocket clients.
// If an error occurs during sending, the client connection is closed and removed.
func handleMessages() {
	for {
		msg, ok := <-broadcast
		if !ok {
			logging.Log("[INFO] broadcast channel closed")
			return
		}

		if msg.Type == "message" || msg.Type == "" {
			messagesMutex.Lock()
			messages = append(messages, msg)
			messagesMutex.Unlock()
			logging.Log("[INFO] Message received for broadcast: %+v", msg)
		}

		for client := range clients {
			envelope := map[string]interface{}{
				"type":    msg.Type,
				"message": msg,
			}
			err := client.WriteJSON(envelope)
			if err != nil {
				logging.Log("[ERROR] Error sending message to client %v: %v", client.RemoteAddr(), err)
				client.Close()
				delete(clients, client)
			} else {
				logging.Log("[INFO] Message sent to client %v: %+v", client.RemoteAddr(), envelope)
			}
		}
	}
}

// MarkMessagesAsRead updates the 'is_read' status of messages in the database.
// It sets messages as read where the receiverID matches the provided receiver and the senderID matches the provided sender.
// Returns an error if the database operation fails.
func MarkMessagesAsRead(receiverID, senderID int) error {
	query := `
        UPDATE messages 
        SET is_read = 1 
        WHERE receiver_id = ? AND user_id = ?
    `
	_, err := database.DB.Exec(query, receiverID, senderID)
	if err != nil {
		logging.Log("Error marking messages as read: %v", err)
		return err
	}
	return nil
}

// SaveMessageToDB saves a new message to the database.
// It inserts the sender's ID, receiver's ID, message content, and sets is_read to 0 (unread).
// Returns an error if the database insertion fails.
func SaveMessageToDB(senderID, receiverID int, text string) error {
	_, err := database.DB.Exec(
		"INSERT INTO messages (user_id, receiver_id, message,is_read) VALUES (?, ?, ?, 0)",
		senderID, receiverID, text,
	)
	return err
}

// getMessages retrieves all messages exchanged between two users.
// It queries the database for messages where either user is the sender or receiver.
// Messages are ordered by creation time. Returns a slice of Message models and an error if the query or scanning fails.
func getMessages(loggedInUserID, otherUserID int) ([]models.Message, error) {
	rows, err := database.DB.Query(
		`SELECT m.message, u.nickname AS sender, m.user_id, m.receiver_id, m.created_at
		 FROM messages m
		 JOIN users u ON m.user_id = u.id
		 WHERE (m.user_id = ? AND m.receiver_id = ?) OR (m.user_id = ? AND m.receiver_id = ?)
		 ORDER BY m.created_at ASC`,
		loggedInUserID, otherUserID,
		otherUserID, loggedInUserID,
	)
	if err != nil {
		return nil, fmt.Errorf("error querying messages: %v", err)
	}
	defer rows.Close()

	var allMessages []models.Message
	for rows.Next() {
		var msg models.Message
		var createdAt time.Time

		if err := rows.Scan(&msg.Data, &msg.Sender, &msg.SenderID, &msg.Receiver, &createdAt); err != nil {
			return nil, fmt.Errorf("error scanning message row: %v", err)
		}

		msg.Time = createdAt.Format("3:04:05 PM")
		allMessages = append(allMessages, msg)
	}

	return allMessages, nil
}

// getLastMessages retrieves the last message for each unique conversation partner of a given user.
// It queries the database to find the most recent message for each conversation.
// Returns a slice of Message models, where each message represents the last communication with a different user.
func getLastMessages(userID int) ([]models.Message, error) {
	query := `
        SELECT m.message, u.nickname as sender_name, m.user_id,
               CASE 
                   WHEN m.user_id = ? THEN m.receiver_id 
                   ELSE m.user_id 
               END as other_user_id,
               CASE
                   WHEN m.user_id = ? THEN (SELECT nickname FROM users WHERE id = m.receiver_id)
                   ELSE (SELECT nickname FROM users WHERE id = m.user_id)
               END as receiver_nickname,
               m.created_at
        FROM messages m
        JOIN users u ON m.user_id = u.id
        JOIN (
            SELECT 
                CASE 
                    WHEN user_id = ? THEN receiver_id 
                    ELSE user_id 
                END as conversation_partner,
                MAX(created_at) as latest_time
            FROM messages
            WHERE user_id = ? OR receiver_id = ?
            GROUP BY conversation_partner
        ) latest ON (
            (m.user_id = ? AND m.receiver_id = latest.conversation_partner) OR
            (m.user_id = latest.conversation_partner AND m.receiver_id = ?)
        ) AND m.created_at = latest.latest_time
        ORDER BY m.created_at DESC
    `

	rows, err := database.DB.Query(query, userID, userID, userID, userID, userID, userID, userID, userID)
	if err != nil {
		fmt.Println("error querying last messages: ", err)
		return nil, fmt.Errorf("error querying last messages: %v", err)
	}
	defer rows.Close()

	var result []models.Message
	for rows.Next() {
		var msg models.Message
		var createdAt time.Time
		var otherUserID int

		if err := rows.Scan(&msg.Data, &msg.Sender, &msg.SenderID, &otherUserID, &msg.ReceiverNickname, &createdAt); err != nil {
			return nil, fmt.Errorf("error scanning message row: %v", err)
		}

		msg.Time = createdAt.Format("3:04:05 PM")
		msg.Receiver = otherUserID
		result = append(result, msg)
	}

	return result, nil
}

// MessageHandler handles incoming HTTP requests related to messages.
// It supports PUT requests to mark messages as read.
// The user must be authenticated to perform these actions.
func MessageHandler(w http.ResponseWriter, r *http.Request) {

	userID, ok := middleware.GetUserID(r)
	if !ok {
		utils.ErrorMessage(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	switch r.Method {
	case http.MethodPut:
		var req struct {
			SenderID int `json:"senderId"`
		}
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logging.Log("Error decoding request body: %v", err)
			utils.ErrorMessage(w, "Invalid request", http.StatusBadRequest)
			return
		}

		userID, ok = middleware.GetUserID(r)
		if !ok {
			utils.ErrorMessage(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		if err := MarkMessagesAsRead(userID, req.SenderID); err != nil {
			logging.Log("Error marking messages as read: %v", err)
			utils.ErrorMessage(w, "Failed to mark messages as read", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		return
	case http.MethodGet:
		if r.URL.Query().Has("receiver") {
			receiverIDStr := r.URL.Query().Get("receiver")

			if receiverIDStr == "" {
				utils.ErrorMessage(w, "Invalid receiver ID", http.StatusBadRequest)
				return
			}
			receiverID, err := strconv.Atoi(receiverIDStr)
			if err != nil {
				logging.Log("Error parsing receiver ID: %v", err)
				utils.ErrorMessage(w, "error parsing the receiverid", http.StatusBadRequest)
				return
			}

			msgs, err := getMessages(userID, receiverID)
			if err != nil {
				logging.Log("Error getting messages: %v", err)
				utils.ErrorMessage(w, "Failed to fetch messages", http.StatusInternalServerError)
				return
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(msgs)
			return
		} else {
			msgs, err := getLastMessages(userID)
			if err != nil {
				logging.Log("Error getting messages: %v", err)
				utils.ErrorMessage(w, "Failed to fetch messages", http.StatusInternalServerError)
				return
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(msgs)
			return
		}

	case http.MethodPost:
		var msg models.Message
		if err := json.NewDecoder(r.Body).Decode(&msg); err != nil {
			logging.Log("Error decoding message: %v", err)
			utils.ErrorMessage(w, "Invalid Request", http.StatusBadRequest)
			return
		}
		senderID, ok := middleware.GetUserID(r)
		if !ok {
			utils.ErrorMessage(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		if err := SaveMessageToDB(senderID, msg.Receiver, msg.Data); err != nil {
			logging.Log("Error saving message to DB: %v", err)
			utils.ErrorMessage(w, "Failed to save message", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusCreated)
	default:
		utils.ErrorMessage(w, "Method not allowed", http.StatusBadRequest)
		return
	}
}
