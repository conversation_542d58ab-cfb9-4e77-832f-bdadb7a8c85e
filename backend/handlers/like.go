package handlers

import (
	"encoding/json"
	"net/http"

	"forum/logging"
	"forum/middleware"
	"forum/models"
	"forum/queries"
	"forum/utils"
)

// CreatePostLikeDislikeHandler handles the creation of likes or dislikes for posts.
// It expects a POST request with a JSON body containing the post ID and whether it's a like or dislike.
// The user must be authenticated. It creates or updates the like/dislike record in the database.
func CreatePostLikeDislikeHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.ErrorMessage(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	userID, ok := middleware.GetUserID(r)
	if !ok {
		utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
		return
	}

	var like models.LikeDislike

	if err := ParseJSONBody(r.Body, &like); err != nil {
		logging.Log("[ERROR] Error parsing JSON: %v", err)
		utils.ErrorMessage(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	like.UserID = userID
	if err := queries.CreateLikeDislike(like); err != nil {
		logging.Log("[ERROR] Error creating like/dislike: %v", err)
		utils.ErrorMessage(w, "Opps... Failed to like/dislike", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]string{"message": "Like/Dislike created successfully"})
}
