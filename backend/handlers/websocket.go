package handlers

import (
	"forum/logging"
	"forum/models"
	"forum/utils"
	"net/http"
	"time"
)

// MessageWebSocketHandler handles WebSocket connections for real-time messaging.
// It upgrades HTTP connection to WebSocket, manages client connections,
// and broadcasts received messages to all connected clients.
// It also handles typing and stopped_typing notifications.
func MessageWebSocketHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		utils.ErrorMessage(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		logging.Log("Error upgrading to WebSocket: %v", err)
		http.Error(w, "Could not open websocket connection", http.StatusBadRequest)
		return
	}
	clients[conn] = true
	defer func() {
		conn.Close()
		delete(clients, conn)
	}()
	for {
		var msg models.Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			logging.Log("[INFO] : reading message: %v", err)
			break
		}

		switch msg.Type {
		case "typing", "stopped_typing":
			// For typing notifications, broadcast directly without saving to DB
			for client := range clients {
				if client != conn { // Don't send typing notification back to the sender
					err := client.WriteJSON(msg)
					if err != nil {
						logging.Log("[ERROR] Error sending typing status to client: %v", err)
						client.Close()
						delete(clients, client)
					}
				}
			}
		default:
			// For regular chat messages
			msg.Type = "message" // Ensure message type is set to chat
			if msg.Time == "" {
				msg.Time = time.Now().Format("15:04:05")
			}
			broadcast <- msg
		}
	}
}
