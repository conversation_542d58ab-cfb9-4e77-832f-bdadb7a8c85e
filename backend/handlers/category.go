package handlers

import (
	"encoding/json"
	"net/http"

	"forum/logging"
	"forum/queries"
	"forum/utils"
)

// GetCategoriesHandler handles requests to retrieve all available categories.
// It expects a GET request. It fetches categories from the database and returns them
// as a JSON array. If an error occurs during retrieval, it logs the error and returns
// an internal server error response.
func GetCategoriesHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}

	categories, err := queries.GetCategories()
	if err != nil {
		logging.Log("[ERROR] Error getting categories: %v", err)
		utils.ErrorMessage(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(categories)
}
