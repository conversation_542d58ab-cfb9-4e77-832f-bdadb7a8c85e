package handlers

import (
	"encoding/json"
	"net/http"

	"forum/logging"
	"forum/middleware"
	"forum/models"
	"forum/queries"
	"forum/utils"
)

// AuthStatusHandler handles requests to check the authentication status of a user.
// It expects a GET request. If the user is authenticated, it returns a JSON response
// with `authenticated: true`, the user's nickname, and user ID. Otherwise, it returns
// `authenticated: false`.
func AuthStatusHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		utils.ErrorMessage(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	userID, ok := middleware.GetUserID(r)

	if !ok {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"authenticated": false,
		})
		return
	}

	var user models.User
	user.ID = userID
	if err := queries.GetUserByID(&user); err != nil {
		logging.Log("[ERROR] Failed to get user info: %v", err)
		http.Error(w, "Failed to get user info", http.StatusInternalServerError)
		return
	}
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")

	json.NewEncoder(w).Encode(map[string]interface{}{
		"authenticated": true,
		"nickname":      user.Nickname,
		"user_id":       user.ID,
	})
}
