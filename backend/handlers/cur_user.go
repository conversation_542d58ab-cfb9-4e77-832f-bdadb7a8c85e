package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"forum/middleware"
)

// CurrentUserHandler handles requests to retrieve the ID of the currently authenticated user.
// It extracts the user ID from the request context and returns it as a JSON response.
func CurrentUserHandler(w http.ResponseWriter, r *http.Request) {
	userID, _ := middleware.GetUserID(r)
	fmt.Println("userID: ", userID)
	json.NewEncoder(w).Encode(userID)
}
