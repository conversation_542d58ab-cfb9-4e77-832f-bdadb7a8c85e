package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	"forum/database"
	"forum/logging"
	"forum/middleware"
	"forum/utils"
)

// UnreadHandler handles requests related to unread messages.
// It expects a GET request and returns the count of unread messages for the authenticated user.
// An optional 'exclude_sender' query parameter can be used to exclude messages from a specific sender.
func UnreadHandler(w http.ResponseWriter, r *http.Request) {

	userID, ok := middleware.GetUserID(r)
	if !ok {
		utils.ErrorMessage(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	switch r.Method {
	case http.MethodGet:

		excludeSenderStr := r.URL.Query().Get("exclude_sender")
		var excludeSender *int
		if excludeSenderStr != "" {
			if id, err := strconv.Atoi(excludeSenderStr); err == nil {
				excludeSender = &id
			} else {
				logging.Log("[ERROR] : Invalid exclude_sender parameter: %s", excludeSenderStr)
			}
		}

		unreadCount, err := getUnreadCount(userID, excludeSender)
		if err != nil {
			logging.Log("[ERROR] Error getting unread count: %v", err)
			utils.ErrorMessage(w, "Failed to fetch unread message count", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(unreadCount)
		return

	default:
		utils.ErrorMessage(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
}

// getUnreadCount retrieves the count of unread messages for a given user.
// It can optionally exclude messages from a specific sender.
// Returns the count of unread messages and an error if the database query fails.
func getUnreadCount(userID int, excludeSender *int) (int, error) {

	var query string
	var args []interface{}

	if excludeSender != nil {
		query = `
			SELECT COUNT(DISTINCT user_id) 
			FROM messages 
			WHERE receiver_id = ? 
			AND is_read = 0
			AND user_id != ?
		`
		args = []interface{}{userID, *excludeSender}
	} else {
		query = `
			SELECT COUNT(DISTINCT user_id) 
			FROM messages 
			WHERE receiver_id = ? 
			AND is_read = 0
		`
		args = []interface{}{userID}
	}

	var count int
	err := database.DB.QueryRow(query, args...).Scan(&count)
	if err != nil {
		logging.Log("[ERROR] Error getting unread count: %v", err)
		return 0, err
	}
	return count, nil
}
