package queries

import (
	"errors"
	"forum/logging"
	"forum/models"
)

// ErrInvalidData is returned when required data is missing
var ErrInvalidData = errors.New("invalid data: missing required fields")

// UpdateCommentLikes updates the like/dislike status for a comment.
// It validates the input data and then calls CreateCommentLikeDislike to handle the database operation.
func UpdateCommentLikes(like models.LikeDislike) error {

	if like.UserID == 0 || like.CommentID == 0 {
		logging.Log("[ERROR] Invalid like/dislike data: UserID=%d, CommentID=%d",
			like.UserID, like.CommentID)
		return ErrInvalidData
	}

	return CreateCommentLikeDislike(like)
}
