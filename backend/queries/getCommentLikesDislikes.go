package queries

import (
	"forum/database"
	"forum/models"
)

// GetCommentLikesDislikes retrieves all likes and dislikes for a specific comment ID.
func GetCommentLikesDislikes(commentID int) ([]models.LikeDislike, error) {
	query := "SELECT id, user_id, comment_id, is_like, created_at FROM comment_likes WHERE comment_id = ?"

	rows, err := database.DB.Query(query, commentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var likes []models.LikeDislike
	for rows.Next() {
		var like models.LikeDislike
		err := rows.Scan(&like.ID, &like.UserID, &like.CommentID, &like.IsLike, &like.CreatedAt)
		if err != nil {
			return nil, err
		}
		likes = append(likes, like)
	}
	return likes, nil
}
