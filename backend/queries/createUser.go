package queries

import (
	"fmt"

	"forum/database"
	"forum/models"
	"time"
)

// <PERSON><PERSON><PERSON><PERSON> inserts a new user into the database.
// It returns the ID of the newly created user and an error if the operation fails.
func CreateUser(user models.User) (int, error) {

	user.CreatedAt = time.Now()

	query := "INSERT INTO users (nickname, age, gender, first_name, last_name, email, password, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"

	result, err := database.DB.Exec(query,
		user.Nickname,
		user.Age,
		user.Gender,
		user.FirstName,
		user.LastName,
		user.Email,
		user.Password,
		user.CreatedAt,
	)

	if err != nil {
		return 0, fmt.Errorf("failed to create user: %v", err)
	}

	lastID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.<PERSON><PERSON><PERSON>("failed to get last insert ID: %v", err)
	}

	return int(lastID), nil
}
