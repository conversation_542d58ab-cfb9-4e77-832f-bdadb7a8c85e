package queries

import (
	"database/sql"

	"forum/database"
	"forum/models"
)

// CreateLikeDislike inserts or updates a like/dislike for a post.
// It checks if a like/dislike already exists for the given user and post.
// If it doesn't exist, a new entry is inserted. If it exists, the existing entry is updated.
func CreateLikeDislike(like models.LikeDislike) error {
	var existingID int
	err := database.DB.QueryRow(
		"SELECT id FROM likes WHERE user_id = ? AND post_id = ?",
		like.UserID, like.PostID,
	).Scan(&existingID)

	if err == sql.ErrNoRows {

		_, err = database.DB.Exec(
			"INSERT INTO likes (user_id, post_id, is_like) VALUES (?, ?, ?)",
			like.UserID, like.PostID, like.IsLike,
		)
		return err
	} else if err != nil {

		return err
	}

	_, err = database.DB.Exec(
		"UPDATE likes SET is_like = ? WHERE id = ?",
		like.IsLike, existingID,
	)
	return err
}
