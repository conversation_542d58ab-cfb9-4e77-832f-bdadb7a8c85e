package queries

import (
	"errors"

	"forum/database"
	"forum/models"
)

// CreatePost inserts a new post into the database.
// It also handles inserting the post's categories into the database.
func CreatePost(post models.Post) error {
	res, err := database.DB.Exec("INSERT INTO posts (user_id, title, content) VALUES (?, ?, ?)", post.UserID, post.Title, post.Content)
	if err != nil {
		return errors.New("failed to insert post into post table")
	}
	postID, err := res.LastInsertId()
	if err != nil {
		return errors.New("failed to get last post id")
	}
	if err := InsertPostCategories(int(postID), post.RawCategories); err != nil {
		return err
	}
	return nil
}
