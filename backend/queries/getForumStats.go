package queries

import "forum/database"

// GetForumStats retrieves various statistics about the forum from the database.
// It returns a map containing the total number of likes, comments, posts, and information about the most active category.
func GetForumStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	var totalLikes int
	err := database.DB.QueryRow(`
		SELECT COUNT(*) FROM likes WHERE is_like = 1
	`).Scan(&totalLikes)
	if err != nil {
		return nil, err
	}

	var totalComments int
	err = database.DB.QueryRow(`
		SELECT COUNT(*) FROM comments
	`).Scan(&totalComments)
	if err != nil {
		return nil, err
	}

	var totalPosts int
	err = database.DB.QueryRow(`
		SELECT COUNT(*) FROM posts
	`).Scan(&totalPosts)
	if err != nil {
		return nil, err
	}

	var mostActiveCategory string
	var categoryCount int
	err = database.DB.QueryRow(`
		SELECT c.name, COUNT(pc.post_id) as post_count
		FROM categories c
		JOIN post_categories pc ON c.id = pc.category_id
		GROUP BY c.id
		ORDER BY post_count DESC
		LIMIT 1
	`).Scan(&mostActiveCategory, &categoryCount)
	if err != nil {
		mostActiveCategory = "None"
		categoryCount = 0
	}

	stats["total_likes"] = totalLikes
	stats["total_comments"] = totalComments
	stats["total_posts"] = totalPosts
	stats["most_active_category"] = mostActiveCategory
	stats["category_post_count"] = categoryCount

	return stats, nil
}
