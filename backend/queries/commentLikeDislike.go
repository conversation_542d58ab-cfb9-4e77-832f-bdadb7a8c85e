package queries

import (
	"database/sql"
	"forum/database"
	"forum/logging"
	"forum/models"
)

// CreateCommentLikeDislike handles both creating and updating comment likes/dislikes.
// It checks if a like/dislike already exists for the given user and comment.
// If it doesn't exist, a new entry is inserted. If it exists, the existing entry is updated.
func CreateCommentLikeDislike(like models.LikeDislike) error {

	var existingID int
	err := database.DB.QueryRow(
		"SELECT id FROM comment_likes WHERE user_id = ? AND comment_id = ?",
		like.UserID, like.CommentID,
	).Scan(&existingID)

	if err == sql.ErrNoRows {

		_, err = database.DB.Exec(
			"INSERT INTO comment_likes (user_id, comment_id, is_like) VALUES (?, ?, ?)",
			like.UserID, like.CommentID, like.IsLike,
		)
		return err
	} else if err != nil {

		logging.Log("[ERROR] Database error checking for existing comment like: %v", err)
		return err
	}

	_, err = database.DB.Exec(
		"UPDATE comment_likes SET is_like = ? WHERE id = ?",
		like.IsLike, existingID,
	)
	return err
}
