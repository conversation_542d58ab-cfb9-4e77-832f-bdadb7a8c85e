package queries

import (
	"database/sql"
	"fmt"

	"forum/models"
)

// scanPosts is a helper function that scans rows from a database query into a slice of Post models.
// It also retrieves and assigns categories to each post.
func scanPosts(rows *sql.Rows) ([]models.Post, error) {
	var posts []models.Post
	for rows.Next() {
		var post models.Post
		err := rows.Scan(
			&post.ID,
			&post.UserID,
			&post.Nickname,
			&post.Title,
			&post.Content,
			&post.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning post: %v", err)
		}

		categories, err := GetCategoriesByPostID(post.ID)
		if err == nil {
			post.Categories = categories
		}

		posts = append(posts, post)
	}
	return posts, nil
}
