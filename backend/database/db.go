package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

// InitDB initializes the database connection and creates necessary tables.
// It creates a 'data' directory if it doesn't exist, opens a SQLite3 database file 'forum.db'
// inside it, and then calls CreateTables to set up the database schema.
func InitDB() (err error) {
	dbDir := "./data"
	if err = os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("failed to create database directory: %v", err)
	}

	dbPath := filepath.Join(dbDir, "forum.db")
	DB, err = sql.Open("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("error opening database: %v", err)
	}
	err = CreateTables(TableCreationStatements)
	if err != nil {
		return fmt.Errorf("error creating tables: %v", err)
	}
	log.Println("Database initialized successfully")
	return
}
