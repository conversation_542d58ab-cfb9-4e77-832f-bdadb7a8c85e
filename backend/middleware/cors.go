package middleware

import "net/http"

// CorsMiddleware is a middleware that adds Cross-Origin Resource Sharing (CORS) headers to HTTP responses.
// It allows requests from specified origins, methods, and headers, and handles preflight OPTIONS requests.
func CorsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		origin := r.Header.Get("Origin")
		if origin != "" {
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Vary", "Origin")
		} else {
			w.Header().Set("Access-Control-Allow-Origin", "*")
		}
		w.<PERSON>er().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, DELETE")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Credentials", "true")

		if r.Method == http.MethodOptions {
			w.<PERSON><PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
			return
		}
		next.ServeHTTP(w, r)
	})
}
