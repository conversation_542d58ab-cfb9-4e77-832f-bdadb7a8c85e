package middleware

import (
	"context"
	"database/sql"
	"net/http"
	"time"

	"forum/database"
	"forum/utils"
)

type contextKey string

const UserIDKey contextKey = "userID"

// AuthMiddleware is a middleware that checks if a user is authenticated via a session token.
// It retrieves the session token from the request cookie, validates it against the database,
// and checks for session expiration. If valid, it adds the user ID to the request context.
// Otherwise, it returns an unauthorized error.
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		cookie, err := r.<PERSON>("session_token")
		if err != nil {
			utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
			return
		}

		var userID int
		var expiresAt time.Time
		err = database.DB.QueryRow(
			"SELECT user_id, expires_at FROM sessions WHERE token = ?",
			cookie.Value,
		).Scan(&userID, &expiresAt)

		if err == sql.ErrNoRows {
			utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
			return
		}
		if err != nil {
			utils.ErrorMessage(w, "Ooops! Please login to continue...", http.StatusInternalServerError)
			return
		}

		if time.Now().After(expiresAt) {
			database.DB.Exec("DELETE FROM sessions WHERE token = ?", cookie.Value)
			utils.ErrorMessage(w, "Please login to continue...", http.StatusUnauthorized)
			return
		}

		ctx := context.WithValue(r.Context(), UserIDKey, userID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetUserID retrieves the user ID from the request context.
// This function should be called after AuthMiddleware has successfully added the user ID to the context.
// Returns the user ID and a boolean indicating if the ID was found.
func GetUserID(r *http.Request) (int, bool) {
	userID, ok := r.Context().Value(UserIDKey).(int)
	return userID, ok
}
