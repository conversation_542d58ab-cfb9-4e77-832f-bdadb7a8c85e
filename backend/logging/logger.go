package logging

import (
	"log"
	"os"
	"path/filepath"
)

// InitializeLogger sets up the logging system for the application.
// It creates a 'logs' directory if it doesn't exist, and opens/creates 'application.log' for file logging.
// It initializes two loggers: one for writing to the log file and another for writing to standard output (terminal).
// All application logs will be directed to both the file and the terminal.
func InitializeLogger() {
	logsDir := "logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		log.Fatal("Failed to create logs directory:", err)
	}

	logFile, err := os.OpenFile(
		filepath.Join(logsDir, "application.log"),
		os.O_CREATE|os.O_WRONLY|os.O_APPEND,
		0644,
	)
	if err != nil {
		log.Fatal("Failed to create log file:", err)
	}

	fileLogger := log.New(logFile, "", log.LstdFlags)
	terminalLogger = log.New(os.Stdout, "", log.LstdFlags)

	logger = fileLogger
	terminalLogger.Println("Logger initialized successfully")
}

var logger *log.Logger

var terminalLogger *log.Logger

// Log writes a formatted log message to the configured application log file.
// It behaves similarly to log.Printf, accepting a format string and variadic arguments.
func Log(format string, v ...any) {
	logger.Printf(format, v...)
}

// TerminalLog writes a formatted log message to the terminal (standard output).
// It is intended for messages that should always be visible in the console, regardless of file logging.
func TerminalLog(format string, v ...interface{}) {
	terminalLogger.Printf(format, v...)
}
