// WebSocketService manages the WebSocket connection for real-time communication.
// It handles connection, reconnection, message queuing, and message dispatching to registered handlers.
class WebSocketService {
    constructor() {
        this.connected = false;
        this.connectAttempts = 0;
        this.maxReconnectDelay = 5000;
        this.handlers = {
            post: new Set(),
            comment: new Set(),
            like: new Set()
        };
        this.messageQueue = [];
        this.connect();
    }

    connect() {
        try {
            const userId = localStorage.getItem('userId');
            if (!userId) {
                console.warn('No userId found, delaying WebSocket connection');
                setTimeout(() => this.connect(), 1000);
                return;
            }

            this.ws = new WebSocket(`ws://localhost:9111/ws?userId=${userId}`);
            
            this.ws.onopen = () => {
    
                this.connected = true;
                this.connectAttempts = 0;
                
                // Process any queued messages
                while (this.messageQueue.length > 0) {
                    const msg = this.messageQueue.shift();
                    this.send(msg.type, msg.action, msg.data);
                }
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);

                    this.handleWebSocketMessage(message);
                } catch (error) {
                    handleError('Error processing WebSocket message.');
                }
            };

            this.ws.onclose = () => {
    
                this.connected = false;
                this.reconnect();
            };

            this.ws.onerror = (error) => {
                handleError('WebSocket error.');
                this.connected = false;
            };
        } catch (error) {
            handleError('Error establishing WebSocket connection.');
            this.reconnect();
        }
    }

    reconnect() {
        if (!this.connected) {
            const delay = Math.min(1000 * Math.pow(2, this.connectAttempts), this.maxReconnectDelay);

            setTimeout(() => {
                this.connectAttempts++;
                this.connect();
            }, delay);
        }
    }

    handleWebSocketMessage(message) {
        try {

            const handlers = this.handlers[message.type];
            if (handlers && handlers.size > 0) {

                handlers.forEach(handler => {
                    try {
                        handler(message);
                    } catch (error) {
            handleError('Error in message handler.');
                    }
                });
            } else {

            }
        } catch (error) {
            handleError('Error handling WebSocket message.');
        }
    }

    subscribe(type, handler) {

        if (this.handlers[type]) {
            this.handlers[type].add(handler);

        }
    }

    unsubscribe(type, handler) {
        if (this.handlers[type]) {
            this.handlers[type].delete(handler);
        }
    }

    send(type, action, data) {
        const message = {
            type,
            action,
            data,
            timestamp: new Date()
        };

        if (!this.connected) {

            this.messageQueue.push(message);
            return;
        }

        try {
            if (this.ws.readyState === WebSocket.OPEN) {

                this.ws.send(JSON.stringify(message));
            } else {

                this.messageQueue.push(message);
            }
        } catch (error) {
            handleError('Error sending WebSocket message.');
            this.messageQueue.push(message);
        }
    }
}

// Create global instance
window.wsService = new WebSocketService();