const inboxBtn = document.getElementById("inbox-btn");
const mainContent = document.getElementById("main-content");

let messages = [];
let lastMessages = [];

let currentUser = null;
let currentUserId = null;
let currentReceiverId = null;
let currentPartner = null;
let unreadCheckInterval;
let messageCache = {};
let messagesPerPage = 10;
let currPage = 1;
let ws;
let endIndex = 0;
let typingTimer;
let typingAnimationInterval;
let lastLoadMoreCall = 0;



/**
 * Marks all messages in a given conversation as read.
 * @param {string} conversationId - The ID of the conversation to mark as read.
 */
async function markRead() {
  try {
    await fetch("/api/protected/api/messages", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        senderId: receiverId,
      }),
    });
    
    checkUnreadMessages();
  } catch (error) {
  }
  
}

/**
 * Checks for unread messages and updates the UI accordingly.
 */
async function checkUnreadMessages() {
  try {
    let url = "/api/protected/api/unread";
    if (currentReceiverId) {
      url += `?exclude_sender=${currentReceiverId}`;
    }
    
    const response = await fetch(url);
    if (response.ok) {
      const count = await response.json();
      const badge = document.getElementById("unread-badge");

      if (count > 0) {
        badge.style.display = "block";
        badge.textContent = count;
      } else {
        badge.style.display = "none";
      }
    }
  } catch (error) {
  }
}

/**
 * Starts a periodic check for unread messages.
 */
function startUnreadCheck() {
  checkUnreadMessages(); 
  unreadCheckInterval = setInterval(checkUnreadMessages, 5000);
}

/**
 * Stops the periodic check for unread messages.
 */
function stopUnreadCheck() {
  if (unreadCheckInterval) {
    clearInterval(unreadCheckInterval);
  }
}

/**
 * Fetches the current user's information from the server.
 */
async function fetchCurrentUser() {
  try {
    const response = await fetch("/api/protected/api/auth/status");
    const data = await response.json();
    ("INFO : fetched current user:",data)
    if (data.authenticated) {
      currentUser = data.nickname;
      currentUserId = data.user_id;
    } else {
      currentUser = null;
      currentUserId = null;
    }
  } catch (e) {
    currentUser = null;
    currentUserId = null;
  }
}

/**
 * Fetches messages for a given receiver ID.
 * @param {string} receiverId - The ID of the receiver.
 */
async  function fetchMessages(receiverId) {
  try {
    const url = new URL("/api/protected/api/messages", window.location.origin);
    url.searchParams.append("receiver", receiverId);
    let res = await fetch(url.toString());
    if (res.ok) {
      messages = await res.json();
      console.log("Fetched messages for receiverId", receiverId, ":", messages);
    } else {
      messages = [];
      console.log("Failed to fetch messages for receiverId", receiverId);
    }
  } catch (e) {
    messages = [];
    console.error("Error fetching messages:", e);
  }
}

/**
 * Fetches the last few messages for a given receiver ID.
 * @param {string} receiverId - The ID of the receiver.
 */
async function fetchLastMessages() {
  try {
    let res = await fetch("/api/protected/api/messages");
    if (res.ok) {
      lastMessages = await res.json();
      console.log("Fetched last messages:", lastMessages);
    } else {
      lastMessages = [];
      console.log("Failed to fetch last messages.");
    }
  } catch (e) {
    lastMessages = [];
    console.error("Error fetching last messages:", e);
  }
}

let inboxRefreshInterval;

/**
 * Starts a periodic refresh of the inbox.
 */
function startInboxRefresh() {
  if (inboxRefreshInterval) {
    clearInterval(inboxRefreshInterval);
  }

  inboxRefreshInterval = setInterval(async () => {
    if (mainContent && mainContent.querySelector(".inbox-section")) {
      await fetchLastMessages();
      renderInbox();
    }
  }, 5000);
}

/**
 * Stops the periodic refresh of the inbox.
 */
function stopInboxRefresh() {
  if (inboxRefreshInterval) {
    clearInterval(inboxRefreshInterval);
    inboxRefreshInterval = null;
  }
}

/**
 * Initializes the inbox by fetching the current user and conversations.
 */
async function initInbox() {
  await fetchCurrentUser();
  await fetchLastMessages();
  initWebSocket(); // Initialize WebSocket when inbox is loaded
}

/**
 * Extracts unique conversations from a list of messages.
 * @param {Array<Object>} messages - The list of messages.
 * @returns {Array<Object>} An array of unique conversations.
 */
function getConversations() {
  if (!Array.isArray(lastMessages) || lastMessages.length === 0) {
    return [];
  }

  const conversationMap = new Map();
  
  lastMessages.forEach((msg) => {
    const isCurrentUserSender = msg.sender === currentUser;
    const partner = isCurrentUserSender ? msg.receiver_nickname : msg.sender;
    const partnerId = isCurrentUserSender ? msg.receiver : msg.sender_id;
    
    let partnerName = partner;
    const onlineUsers = document.querySelectorAll(".online-user");
    onlineUsers.forEach((user) => {
      if (user.dataset.receiverId == partnerId) {
        partnerName = user.querySelector(".receiver").textContent;
      }
    });
    
    if (!conversationMap.has(partner) || 
        new Date(msg.time) > new Date(conversationMap.get(partner).lastMsg.time)) {
      conversationMap.set(partner, {
        partner: partnerName,
        lastMsg: {
          data: msg.data || "",
          time: msg.time || "",
          receiver: msg.receiver,
          receiverId: isCurrentUserSender ? msg.receiver : msg.sender_id,
        },
      });
    }
  });
  
  return Array.from(conversationMap.values());
}
/**
 * Renders the inbox view, displaying a list of conversations.
 */
async function renderInbox() {
  currentPartner = null;
  currentReceiverId = null;
  
  document.getElementById("toggleRight").style.display = "flex";
  document.getElementById("toggleLeft").style.display = "flex";
  if (!currentUser) {
    mainContent.innerHTML = "<div>Please log in to view your inbox.</div>";
    return;
  }

  await fetchLastMessages();

  startInboxRefresh();

  const conversations = getConversations();
  let inboxHTML = `
    <div class="inbox-section">
        <h2>Inbox</h2>
        <div class="conversation-list">
            ${
              conversations.length === 0
                ? "<div>No conversations yet.</div>"
                : conversations
                    .map((conv) => {
                      const partner = conv.partner || "";
                      const receiverId =
                        conv.lastMsg && conv.lastMsg.receiverId
                          ? parseInt(conv.lastMsg.receiverId)
                          : 0;
                      const lastMsgText = conv.lastMsg
                        ? conv.lastMsg.data || conv.lastMsg.data || ""
                        : "";
                      const lastMsgTime =
                        conv.lastMsg && conv.lastMsg.time
                          ? conv.lastMsg.time
                          : "";
                      return `
                    <div class="conversation-item" 
                         data-username="${partner}" 
                         data-receiver-id="${receiverId}">
                        <div class="avatar">${
                          partner[0] ? partner[0].toUpperCase() : "?"
                        }</div>
                        <div class="conv-details">
                            <div class="conv-name">${partner}</div>
                            <div class="conv-preview">
                                ${
                                  lastMsgText.length > 32
                                    ? lastMsgText.slice(0, 32) + "…"
                                    : lastMsgText
                                }
                            </div>
                            <div class="conv-time">${lastMsgTime}</div>
                        </div>
                    </div>
                `;
                    })
                    .join("")
            }
        </div>
    </div>
`;
  mainContent.innerHTML = inboxHTML;

  const convList = mainContent.querySelector(".conversation-list");
  if (convList) {
    convList.onclick = (e) => {
      const item = e.target.closest(".conversation-item");
      if (item) {
        const username = item.dataset.username;
        const receiverId = parseInt(item.dataset.receiverId) || 0;
        renderChat(username, receiverId);
      }
    };
  }

  checkUnreadMessages();
}

window.addEventListener("beforeunload", () => {
  stopUnreadCheck();
  stopInboxRefresh();
});
/**
 * Renders the chat view for a specific partner.
 * @param {string} partner - The username of the chat partner.
 * @param {number} receiverId - The ID of the receiver.
 */
async function renderChat(partner, receiverId) {
  ("DEBUG: Opening chat with:", partner);

  const leftSidebar = document.querySelector(".sidebar-left");
  const rightSidebar = document.querySelector(".sidebar-right");

  [leftSidebar, rightSidebar].forEach((sb) => {
    sb.classList.remove("active");
  });

  document.getElementById("toggleRight").style.display = "none";
  document.getElementById("toggleLeft").style.display = "none";

  document.getElementById("unread-badge").style.display = "none";

  startInboxRefresh();

  currentPartner = partner;
  currentReceiverId = receiverId;
  if (!currentUser || !receiverId) {
    renderInbox(); // Go back to inbox if details are missing
    return;
  }

  await fetchMessages(receiverId);

  if (!Array.isArray(messages)) {
    messages = [];
  }
  (messages)
  
  
    messageCache[receiverId] = {
      allMessages: messages,
      loadedCount: 0,
    };

  const allMsgsForChat = messageCache[receiverId].allMessages;
  let initialMessagesToDisplay = [];

  if (allMsgsForChat.length <= messagesPerPage) {
    initialMessagesToDisplay = allMsgsForChat;
  } else {
    initialMessagesToDisplay = allMsgsForChat.slice(-messagesPerPage);
  }

  messageCache[receiverId].loadedCount = initialMessagesToDisplay.length;

  const chatMessages = initialMessagesToDisplay.filter((msg) => {
    if (!msg) return false;
    return (
      (msg.sender === currentUser && msg.receiver === receiverId) ||
      (msg.sender === partner && msg.receiver === currentUserId)
    );
  });

  try {
    await fetch("/api/protected/api/messages", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        senderId: receiverId,
      }),
    });
    
    checkUnreadMessages();
  } catch (error) {
  }


  let chatHTML = `
        <div class="chat-section">
            <button id="back-to-inbox" class="back-btn">← Back to Inbox</button>
            <h2>Chat with ${partner}</h2>
            <div id="typing-indicator" style="display: none; font-size: 0.8em; color: gray; margin-bottom: 5px;"></div>
            <button id="load-more-btn" class="load-more-btn">Load Previous Messages</button>
            <div id="no-messages" class="no-messages" style="display: ${chatMessages.length === 0 ? 'block' : 'none'}">No messages yet. Start the conversation!</div>

            <div class="chat-messages">
                ${chatMessages
                    .map(
                      (msg) => `
                    <div class="chat-msg ${
                      msg.sender === currentUser ? "sent" : "received"
                    }">
                        <div class="msg-content">
                        <span class="msg-text">${msg.data}</span>
                        <div class="msg-details">
                        <span class="msg-sender">${msg.sender === currentUser ? currentUser : msg.sender}</span>
                            <span class="msg-time">${msg.time}</span>
                        </div>
                        </div>
                    </div>
                `
                        )
                        .join("")
                }
            </div>
            <form id="send-msg-form" class="send-msg-form">
                <input type="text" id="msg-input" placeholder="Type a message..." autocomplete="off" required />
                <button type="submit">Send</button>
            </form>
        </div>
    `;
  mainContent.innerHTML = chatHTML;


  
  messagesContainer = document.querySelector(".chat-messages");

  if (messagesContainer) {
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  document.getElementById("back-to-inbox").onclick = () => {
    markRead(receiverId);
    renderInbox()
  };
  const loadMoreBtn = document.getElementById("load-more-btn");
  if (loadMoreBtn) {
    loadMoreBtn.onclick = () => loadMoreMessages(receiverId);
  }
  let typingTimeout;
  document.getElementById("msg-input").onkeyup = async (e) => {
    // Only send typing notification if the key pressed is not Enter
    if (e.key !== "Enter") {
      clearTimeout(typingTimeout);
      typingTimeout = setTimeout(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(
            JSON.stringify({
              type: "typing",
            })
          );
        }
      }, 300); // Send typing notification after 300ms of inactivity
    }
  }
  document.getElementById("send-msg-form").onsubmit = async (e) => {
    e.preventDefault();
    const input = document.getElementById("msg-input");
    const text = input.value.trim();
    const sanitizedText=sanitizeHTML(text);
    ("sending message: ", sanitizedText);
    if (!text) return;

    if (!receiverId) {
      return;
    }
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          sender: currentUser,
          receiver: receiverId,
          type: "message",
          data: sanitizedText,
          time: new Date().toLocaleTimeString(),
        })
      );
    } else {
      console.error("WebSocket is not open or not initialized.");
      return;
    }
    
    messageCache[receiverId].allMessages.push({
      sender: currentUser,
      receiver: receiverId,
      data: sanitizedText,
      time: new Date().toLocaleTimeString()
    });
    messageCache[receiverId].loadedCount++;

    try {
      const response = await fetch("/api/protected/api/messages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          receiver: receiverId,
          data: sanitizedText,
        }),
      });
      const newMessage={
        sender: currentUser,
        receiver: receiverId,
        data: sanitizedText,
        time: new Date().toLocaleTimeString(),
      }
      if (response.ok) {
        // Add the sent message to the local messages array
        messages.push(newMessage);
        appendNewMessage(newMessage)
      } else {
      }
    } catch (error) {
    }

    input.value = "";

    const noMessagesDiv = document.getElementById('no-messages');
    if (noMessagesDiv) {
        noMessagesDiv.style.display = 'none';
    }
    const typingDiv = document.getElementById("typing-indicator");
    if (typingDiv) {
      typingDiv.style.display = "none";
      clearInterval(typingAnimationInterval);
    }
  };
  const messagesContainerScroll = document.querySelector(".chat-messages");
  if (chatMessages.length > 9) {
    messagesContainerScroll.addEventListener("scroll", () => {
      if (messagesContainerScroll.scrollTop <= 3) {
        loadMoreMessages(receiverId);
      }
    });
  }
}

/**
 * Loads more messages for the current chat and prepends them to the display.
 * @param {string} receiverId - The ID of the receiver for whom to load messages.
 */
function loadMoreMessages(receiverId) {
  const now = Date.now();
  if (now - lastLoadMoreCall < 1000) {
    return;
  }
  lastLoadMoreCall = now;
  
  const cache = messageCache[receiverId];
  const allMessages = cache.allMessages;
  const loadedCount = cache.loadedCount;

  if (loadedCount < allMessages.length) {
    const startIndex = Math.max(0, allMessages.length - loadedCount - messagesPerPage);
    const endIndex = allMessages.length - loadedCount;
    const messagesToPrepend = allMessages.slice(startIndex, endIndex);

    cache.loadedCount += messagesToPrepend.length;

    const messagesContainer = document.querySelector(".chat-messages");
    const messagesHTML = messagesToPrepend
      .map(
        (msg) => `
      <div class="chat-msg ${msg.sender === currentUser ? "sent" : "received"}">
        <div class="msg-content">
        <span class="msg-text">${sanitizeHTML(msg.data)}</span>
         <div class="msg-details">
        <span class="msg-sender">${msg.sender === currentUser ? currentUser : msg.sender}</span>
          <span class="msg-time">${msg.time}</span>
          </div>
          </div>
      </div>
    `
      )
      .join("");

    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = messagesHTML;
    
    const currentScrollHeight = messagesContainer.scrollHeight;
    messagesContainer.prepend(...tempDiv.childNodes);
    
    const newScrollHeight = messagesContainer.scrollHeight;
    messagesContainer.scrollTop = newScrollHeight - currentScrollHeight;
  }
}

/**
 * Sanitizes HTML strings to prevent XSS attacks.
 * @param {string} str - The input string to sanitize.
 * @returns {string} The sanitized string.
 */
function sanitizeHTML(str) {
  if (typeof str !== 'string') return '';
  const temp = document.createElement('div');
  temp.textContent = str; 
  return temp.innerHTML;  
}


/**
 * Initializes the WebSocket connection for real-time messaging.
 */
function initWebSocket() {
  if (!currentUser || !currentUserId) {
    console.warn("Cannot initialize WebSocket: currentUser or currentUserId is not available.");
    return;
  }
  if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
    console.log("WebSocket already connected or connecting. State:", ws.readyState);
    return; 
  }
  console.log("Attempting to initialize new WebSocket connection...");
  ws = new WebSocket("ws://" + window.location.host + "/api/protected/api/messaging"); 

  ws.onopen = () => {
    console.log("WebSocket connected successfully.");
    ws.send(JSON.stringify({ type: "auth", user: currentUser }));
  };

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log("Received message from WebSocket:", data);
      if (data.type === "typing"){
        const typingDiv = document.getElementById("typing-indicator");
        if (typingDiv) {
          typingDiv.style.display = "block";
          let dots = 0;
          clearInterval(typingAnimationInterval);
          typingAnimationInterval = setInterval(() => {
            dots = (dots % 3) + 1;
            typingDiv.textContent = `${data.sender_nickname} is typing${'.'.repeat(dots)}`;
          }, 500);
          clearTimeout(typingTimer);
          typingTimer = setTimeout(() => {
            typingDiv.style.display = "none";
            clearInterval(typingAnimationInterval);
          }, 1500); // Hide after 1.5 seconds of inactivity
        }
        return;
      }

      if (data.type === "message" && data.message) {
        const typingDiv = document.getElementById("typing-indicator");
        if (typingDiv) {
          typingDiv.style.display = "none";
          clearInterval(typingAnimationInterval);
          clearTimeout(typingTimer);
        }
        const message = {
          sender: data.message.sender || "",
          data: data.message.data || "",
          time: data.message.time || new Date().toLocaleTimeString(),
          receiver: data.message.receiver || 0,
        };

        if (
          (message.sender === currentUser &&
            message.receiver === currentReceiverId) ||
          (message.sender === currentPartner &&
            message.receiver === currentUserId)
        ) {
          messageCache[currentReceiverId].allMessages.push(message);
          messageCache[currentReceiverId].loadedCount++;

          const currentChat = document.querySelector(".chat-section h2");
          if (
            currentChat &&
            currentPartner &&
            (message.sender === currentPartner ||
              message.receiver === currentPartner)
          ) {
            appendNewMessage(message)
          }else{

            checkUnreadMessages();
          }
        }
      }
    } catch (error) {
      console.error("Error parsing WebSocket message:", error);
    }
  };

  ws.onclose = (event) => {
    console.log("WebSocket disconnected. Code:", event.code, "Reason:", event.reason, "WasClean:", event.wasClean);
    console.log("Retrying WebSocket connection in 2 seconds...");
    setTimeout(initWebSocket, 2000); 
  };

  ws.onerror = (error) => {
    console.error("WebSocket error:", error);
  };
}

/**
 * Appends a new message to the chat display.
 * @param {object} message - The message object to append.
 */
function appendNewMessage(message) {
  const messagesContainer = document.querySelector(".chat-messages");
  if (!messagesContainer) return;
  
  const messageDiv = document.createElement('div');
  messageDiv.className = `chat-msg ${message.sender === currentUser ? "sent" : "received"}`;
  
  messageDiv.innerHTML = `
    <div class="msg-content">
                        <span class="msg-text">${sanitizeHTML(message.data)}</span>
                        <div class="msg-details">
                        <span class="msg-sender">${message.sender === currentUser ? currentUser : message.sender}</span>
                            <span class="msg-time">${message.time}</span>
                        </div>
    </div>
  `;
  
  messagesContainer.appendChild(messageDiv);
  
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

if (inboxBtn && mainContent) {
  inboxBtn.addEventListener("click", () => renderInbox()
);

}

// Expose function globally for status.js and conversation items
window.openInboxWithUser = function (nickname, receiverId) {
  initWebSocket();
  renderChat(nickname, receiverId);
};

