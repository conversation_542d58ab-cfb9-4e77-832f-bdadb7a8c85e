// renderComments generates HTML for displaying a list of comments for a given post.
/**
 * Renders a list of comments for a given post.
 * It takes an array of comment objects and the post ID, returning a string of HTML.
 * If no comments are provided, it returns a message indicating that.
 * @param {Array<Object>} comments - An array of comment objects, each containing content, username, created_at, id, likes, and dislikes.
 * @param {number} postId - The ID of the post to which these comments belong.
 * @returns {string} - An HTML string representing the rendered comments.
 */
function renderComments(comments, postId) {
    if (!Array.isArray(comments) || comments.length === 0) {
        return '<p class="no-comments">No comments yet</p></br>';
    }

    return comments.map(comment => `
        <div class="comment">
            <div class="comment-content">
                <p>${comment.content}</p>
            </div>
            <div class="comment-footer">
                <small>By ${comment.username} on ${new Date(comment.created_at).toLocaleString()}</small>
                <div class="comment-actions">
                    <button onclick="handleCommentLike(${comment.id}, ${postId}, true)" class="action-btn">
                        👍 <span>${comment.likes || 0}</span>
                    </button>
                    <button onclick="handleCommentLike(${comment.id}, ${postId}, false)" class="action-btn">
                        👎 <span>${comment.dislikes || 0}</span>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}
/**
 * Shows or hides the comments section for a specific post.
 * It takes the post ID as input and toggles the display style of the corresponding comments container.
 * @param {number} postId - The ID of the post whose comments section needs to be toggled.
 */
function toggleComments(postId) {
    const container = document.getElementById(`comments-container-${postId}`);
    if (container.style.display === 'none') {
        container.style.display = 'block';
    } else {
        container.style.display = 'none';
        openCommentSections.delete(postId);
    }
}
/**
 * Makes the comment form and its container visible for a specific post.
 * It takes the post ID as input and sets the display style of both the comments container and the comment form to 'block'.
 * @param {number} postId - The ID of the post for which the comment form should be shown.
 */
function showCommentForm(postId) {
    const commentsContainer = document.getElementById(`comments-container-${postId}`);
    const form = document.getElementById(`comment-form-${postId}`);
    commentsContainer.style.display = 'block';
    form.style.display = 'block';
}
/**
 * Handles the submission of a new comment for a specific post.
 * It validates the comment content, checks user authentication, sends the comment to the server, and updates the UI.
 * @param {number} postId - The ID of the post to which the comment is being submitted.
 */
async function submitComment(postId) {
    const commentForm = document.getElementById(`comment-form-${postId}`);
    const textarea = commentForm.querySelector('textarea');
    const content = textarea.value.trim();

    if (!content) {
        handleError('Comment cannot be empty');
        return;
    }

    try {
        // First check authentication status
        const authResponse = await fetch('/api/protected/api/auth/status');
        const authData = await authResponse.json();
        
        if (!authData.authenticated) {
            handleError('Please login to comment');
            return;
        }

        const response = await fetch('/api/protected/api/comments', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                post_id: postId,
                content: sanitizeHTML(content)
            })
        });

        if (!response.ok) {
            throw new Error('Failed to submit comment');
        }

        // Wait for the response before clearing the form
        await response.json();
        
        textarea.value = '';
        commentForm.style.display = 'none';
        
        // Store the current open comments
        const openComments = new Set();
        document.querySelectorAll('.comments-container').forEach(container => {
            if (container.style.display !== 'none') {
                openComments.add(parseInt(container.id.split('-').pop()));
            }
        });
        
        // Fetch the updated post data
        await fetchPosts();
        
        // Restore open state of comments
        openComments.forEach(id => {
            const container = document.getElementById(`comments-container-${id}`);
            if (container) {
                container.style.display = 'block';
            }
        });
        
        handleSuccess('Comment posted successfully');
    } catch (error) {
        handleError('Failed to post comment');
    }
}
/**
 * Processes a like or dislike action on a specific comment.
 * It checks user authentication, sends the like/dislike status to the server, and updates the post's display.
 * @param {number} commentId - The ID of the comment being liked or disliked.
 * @param {number} postId - The ID of the post to which the comment belongs.
 * @param {boolean} isLike - True if it's a like, false if it's a dislike.
 */
async function handleCommentLike(commentId, postId, isLike) {
    try {
        // Check authentication first
        const authResponse = await fetch('/api/protected/api/auth/status');
        const authData = await authResponse.json();

        if (!authData.authenticated) {
            handleError('Please login to like comments');
            return;
        }

        const response = await fetch('/api/protected/api/comments/like', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                comment_id: commentId,
                is_like: isLike
            })
        });

        if (!response.ok) {
            throw new Error('Failed to update like');
        }

        // Update just the specific post instead of fetching all posts
        await updatePost(postId);

        handleSuccess(isLike ? 'Comment liked!' : 'Comment disliked!');
    } catch (error) {
        handleError('Failed to update like');
    }
}
