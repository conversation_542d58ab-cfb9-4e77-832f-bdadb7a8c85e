/**
 * Displays a toast notification with a given message and type.
 * The toast automatically fades out and is removed after a delay.
 * @param {string} message - The message to display in the toast.
 * @param {string} [type='error'] - The type of toast (e.g., 'error', 'success').
 */
function showToast(message, type = 'error') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = document.createElement('i');
    icon.className = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
    
    const messageSpan = document.createElement('span');
    messageSpan.textContent = message;
    
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '&times;';
    closeBtn.onclick = () => toast.remove();
    
    toast.appendChild(icon);
    toast.appendChild(messageSpan);
    toast.appendChild(closeBtn);
    
    const container = document.getElementById('toast-container');
    container.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

/**
 * Displays an error toast message to the user.
 * @param {string} message - The error message to display.
 */
function handleError(message) {
    showToast(message, 'error');
}

/**
 * Displays a success toast message to the user.
 * @param {string} message - The success message to display.
 */
function handleSuccess(message) {
    showToast(message, 'success');
}