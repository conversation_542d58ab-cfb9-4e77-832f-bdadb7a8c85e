let currentPage = 1;
let isLoading = false;
/**
 * @type {boolean} - Flag to indicate if there are more posts to load from the server.
 */
let hasMorePosts = true;
/**
 * @type {string} - Stores the currently active category filter for posts.
 */
let currentCategory = '';
/**
 * @type {string} - Stores the currently active filter type for posts (e.g., 'liked', 'my_posts').
 */
let currentFilter = '';
/**
 * @type {string} - The base URL for fetching posts, which can be modified based on filters.
 */
let fetchPostURL = 'api/protected/api/posts';

/**
 * Sanitizes a given string to prevent XSS attacks.
 * It creates a temporary div element, sets its textContent to the input string,
 * and then returns the innerHTML, effectively escaping any HTML entities.
 * @param {string} str - The string to sanitize.
 * @returns {string} The sanitized string.
 */
function sanitizeHTML(str) {
    if (typeof str !== 'string') return '';
    const temp = document.createElement('div');
    temp.textContent = str; // This automatically escapes HTML
    return temp.innerHTML;
}

/**
 * Fetches categories from the API and dynamically populates the category filter section.
 * It also attaches event listeners to the category filter buttons to handle filtering.
 */
async function loadFilterCategories() {
    try {
        const response = await fetch('api/protected/api/categories');
        const categories = await response.json();
        const container = document.getElementById('categoryFilter');
        

        const categoryIcons = {
            "Technology": "fas fa-laptop-code",
            "Sports": "fas fa-football-ball",
            "Entertainment": "fas fa-film",
            "Science": "fas fa-flask",
            "Politics": "fas fa-landmark",
            "Health": "fas fa-heartbeat",
            "Travel": "fas fa-plane",
            "Food": "fas fa-utensils",
            "Other": "fas fa-question-circle"
        };


        container.innerHTML = `
            <button 
                class="category-filter-btn active"
                data-category="">
                <i class="fas fa-list"></i> All Posts
            </button>
        `;


        container.innerHTML += categories.map(category => `
            <button 
                class="category-filter-btn"
                data-category="${category.id}">
                <i class="${categoryIcons[category.name] || 'fas fa-tag'}"></i> ${category.name}
            </button>
        `).join('');


        container.querySelectorAll(".category-filter-btn").forEach(button => {
            button.addEventListener("click", function() {

                container.querySelectorAll(".category-filter-btn").forEach(btn => btn.classList.remove("active"));
                
                this.classList.add("active");
                
                if (this.dataset.category === 'all') {
                    resetPosts();
                } else {
                    applyCategoryFilter(this.dataset.category);
                }
            });
        });

        fetchPosts();

    } catch (error) {
        handleError('Error loading categories.');
    }
}

/**
 * Fetches categories from the API and populates the category selection
 * checkboxes in the create post modal.
 */
async function loadPostCategories() {
    try {
        const response = await fetch('api/protected/api/categories');
        const categories = await response.json();
        const container = document.getElementById('postCategories');
        
        container.innerHTML = categories.map(category => `
            <label class="category-checkbox">
                <input type="checkbox" value="${category.id}">
                <span>${category.name}</span>
            </label>
        `).join('');
    } catch (error) {
        handleError('Error loading categories.');
    }
}

/**
 * Checks user authentication status and, if authenticated,
 * displays the create post modal and loads post categories.
 */
async function openCreatePostModal() {
    try {
        const response = await fetch('/api/protected/api/auth/status');
        const data = await response.json();

        if (!data.authenticated) {
            handleError('Please login to create post');
            return;
        }

        // If authenticated, show modal and load categories
        document.getElementById('createPostModal').classList.add('active');
        loadPostCategories();
        

    } catch (error) {
        handleError('Error checking authentication status.');
        handleError('Please login to create a post');
    }
}

/**
 * Creates and returns a DOM element for a given post object.
 * It sanitizes post content, handles truncation for long posts, and includes
 * interactive elements like like/dislike buttons, comment toggles, and category tags.
 * @param {object} post - The post object containing details like title, content, author, etc.
 * @returns {HTMLElement} The created post DOM element.
 */
function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post';
    postDiv.dataset.postId = post.id;
    

    const sanitizedTitle = sanitizeHTML(post.title);
    const sanitizedContent = sanitizeHTML(post.content);
    

    const isLongPost = sanitizedContent.length > 800;
    const truncatedContent = isLongPost ? sanitizedContent.slice(0, 800) + '...' : sanitizedContent;
    
    postDiv.innerHTML = `
        <div class="post-header">
            <h3>${sanitizedTitle}</h3>
            <small class="post-meta-info">Posted by ${sanitizeHTML(post.nickname)} on ${new Date(post.created_at).toLocaleString()}</small>
        </div>
        <div class="post-content">
            <p>${truncatedContent}</p>
            ${isLongPost ? `
                <div class="read-more-section">
                    <button onclick="toggleFullPost(this, \`${encodeURIComponent(sanitizedContent)}\`)" class="read-more-btn">
                        Read More
                    </button>
                </div>
            ` : ''}
        </div>
        <div class="post-footer">
            <div class="categories">
                ${post.categories ? post.categories.map(cat => 
                    `<span class="category-tag">${sanitizeHTML(cat.name)}</span>`
                ).join('') : ''}
            </div>
            <div class="post-actions">
                <button onclick="handleLike(${post.id}, true)" class="like-btn">
                    👍 <span>${post.likes || 0}</span>
                </button>
                <button onclick="handleLike(${post.id}, false)" class="dislike-btn">
                    👎 <span>${post.dislikes || 0}</span>
                </button>
                <button onclick="toggleComments(${post.id})" class="comment-btn">
                    💬 Comments (${(post.comments || []).length})
                </button>
            </div>
        </div>
        <div class="comments-container" id="comments-container-${post.id}" style="display: none;">
            <div class="comments-section" id="comments-${post.id}">
                ${renderComments(post.comments || [], post.id)}
            </div>
            <button onclick="showCommentForm(${post.id})" class="add-comment-btn">Add Comment</button>
            <div class="comment-form" id="comment-form-${post.id}" style="display: none;">
                <textarea placeholder="Write a comment..."></textarea>
                <button onclick="submitComment(${post.id})">Submit</button>
            </div>
        </div>
    `;
    return postDiv;
}

/**
 * Expands or collapses the full content of a post.
 * @param {HTMLElement} button - The button element that triggered the toggle.
 * @param {string} content - The full content of the post, URI-encoded.
 */
function toggleFullPost(button, content) {
    const decodedContent = decodeURIComponent(content);
    const postContent = button.closest('.post-content');
    const paragraph = postContent.querySelector('p');
    
    if (button.textContent === 'Read More') {
        paragraph.innerHTML = decodedContent;
        button.textContent = 'Show Less';
    } else {
        paragraph.innerHTML = decodedContent.slice(0, 800) + '...';
        button.textContent = 'Read More';
        // Scroll back to the top of the post
        button.closest('.post').scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * Fetches posts from the server based on current filters and pagination.
 * Prevents multiple simultaneous requests and updates the `hasMorePosts` flag.
 */
async function fetchPosts(append = false) {
    if (isLoading || (!append && !hasMorePosts)) return;
    
    try {
        isLoading = true;
        const response = await fetch(`${fetchPostURL}?page=${currentPage}&category=${currentCategory}&filter=${currentFilter}`);
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to fetch posts');
        }
        const posts = await response.json();
        const mainContent = document.getElementById('main-content');
         // Ensure posts-list exists inside main-content
         let postsList = document.getElementById('posts-list');
         if (!postsList) {
             postsList = document.createElement('div');
             postsList.id = 'posts-list';
             mainContent.innerHTML= '';
             mainContent.appendChild(postsList);
            }
        // const postsList = document.getElementById('posts-list');
        if (!posts || posts.length === 0) {
            if (!append) {
                postsList.innerHTML = '<p>No posts yet. Be the first to create one!</p>';
            }
            hasMorePosts = false;
            return;
        }
        // Check if we've reached the end
        if (posts.length < 8) {
            hasMorePosts = false;
        }
        
        if (!append) {
            postsList.innerHTML = '';
        }

        posts.forEach(post => {
            const postElement = createPostElement(post);
            postsList.appendChild(postElement);
        });

        // Re-setup infinite scroll after adding new posts
        setupInfiniteScroll();

    } catch (error) {
        handleError(error.message);
    } finally {
        isLoading = false;
    }
}

// closeCreatePostModal hides the create post modal and resets the form.
function closeCreatePostModal() {
    document.getElementById('createPostModal').classList.remove('active');
    document.getElementById('createPostForm').reset();
}

// post and title validation with HTML checking
// validatePostForm validates the title and content of a new post.
// It checks for empty fields and title length, displaying error messages if validation fails.
function validatePostForm() {
    const titleInput = document.getElementById('postTitle');
    const contentInput = document.getElementById('postContent');
    const title = titleInput.value.trim();
    const content = contentInput.value.trim();
    
    if (!title) {
        showPostError('Please enter a post title');
        return false;
    }
    
    // Check title length
    if (title.length > 32) {
        showPostError('Title must be 32 characters or less');
        titleInput.value = title.substring(0, 32);
        return false;
    }
    
    if (!content) {
        showPostError('Please enter post content');
        return false;
    }
    
    return true;
}

// Add this helper function for consistent error styling
// showPostError displays an error message within the post creation form.
// It takes a message string and updates the designated error display area.
function showPostError(message) {
    const errorDiv = document.getElementById('post-error-message');
    errorDiv.innerHTML = `
        <div class="error-content">
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        </div>
    `;
    errorDiv.style.display = 'block';
}

// Modified create post handler with enhanced validation and sanitization
// handleCreatePost handles the submission of the create post form.
// It validates the form, sanitizes input, sends the post data to the server,
// and provides feedback to the user upon success or failure.
async function handleCreatePost(event) {
    event.preventDefault();
    
    if (!validatePostForm()) {
        return;
    }

    // Get and sanitize the values
    const title = sanitizeHTML(document.getElementById('postTitle').value.trim()).substring(0, 32);
    const content = sanitizeHTML(document.getElementById('postContent').value.trim());
    const selectedCategories = Array.from(document.querySelectorAll('#postCategories input:checked')).map(input => parseInt(input.value));

    try {
        const response = await fetch('/api/protected/api/posts/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ title, content, raw_categories: selectedCategories })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to create post');
        }

        handleSuccess('Post created successfully');
        closeCreatePostModal();
        resetPosts();
    } catch (e) {
        handleError(e.message);
    }
}

// handleLike processes a user's like or dislike action on a post.
// It checks for user authentication, sends the like/dislike status to the server,
// and updates the post's like/dislike counts on the UI.
async function handleLike(postId, isLike) {
    try {
        const authResponse = await fetch('/api/protected/api/auth/status');
        const authData = await authResponse.json();
        
        if (!authData.authenticated) {
            handleError('Please login to like posts');
            return;
        }

        const response = await fetch('/api/protected/api/likes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                post_id: postId,
                is_like: isLike
            })
        });

        if (!response.ok) {
            throw new Error('Login to leave a like');
        }

        await updatePost(postId);
        handleSuccess(isLike ? 'Post liked!' : 'Post disliked!');
    } catch (error) {
        handleError('Login to like posts');
    }
}

// setupInfiniteScroll initializes the infinite scrolling functionality.
// It creates and observes a 'load-more-trigger' element to detect when the user
// scrolls near the bottom of the page, triggering the loading of more posts.
function setupInfiniteScroll() {
    // Remove any existing trigger
    const existingTrigger = document.getElementById('load-more-trigger');
    if (existingTrigger) {
        existingTrigger.remove();
    }

    const loadMoreTrigger = document.createElement('div');
    loadMoreTrigger.id = 'load-more-trigger';
    loadMoreTrigger.innerHTML = `
        <div class="loading-spinner" style="display: none;"></div>
        <p class="no-more-posts" style="display: none;">No more posts to load</p>
    `;
    document.getElementById('posts-list').appendChild(loadMoreTrigger);

    updateNoMorePostsVisibility();

    // Set up intersection observer for infinite scroll
    const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMorePosts && !isLoading) {
            loadMorePosts();
        } else if (entries[0].isIntersecting && !hasMorePosts) {
            updateNoMorePostsVisibility();
        }
    }, { threshold: 0.1 });
    observer.observe(loadMoreTrigger);
}

// loadMorePosts is triggered by the infinite scroll observer to fetch and display
// additional posts when the user scrolls near the bottom of the page.
async function loadMorePosts() {
    if (isLoading || !hasMorePosts) return;
    
    const trigger = document.getElementById('load-more-trigger');
    const spinner = trigger.querySelector('.loading-spinner');
    
    spinner.style.display = 'inline-block';
    
    currentPage++;
    await fetchPosts(true);
    
    spinner.style.display = 'none';
    
    updateNoMorePostsVisibility();
}

// updates the visibility of the "no more posts" message
// updateNoMorePostsVisibility controls the display of the "No more posts to load" message.
// It shows the message if there are no more posts to fetch, and hides it otherwise.
function updateNoMorePostsVisibility() {
    const trigger = document.getElementById('load-more-trigger');
    if (!trigger) return;

    const noMorePosts = trigger.querySelector('.no-more-posts');
    if (!hasMorePosts) {
        noMorePosts.style.display = 'block';
    } else {
        noMorePosts.style.display = 'none';
    }
}

// Update the existing functions
// resetPosts resets the post display to its initial state.
// It clears current filters, resets pagination, and fetches the first page of posts.
function resetPosts() {
    currentPage = 1;
    currentFilter = '';
    currentCategory = '';
    hasMorePosts = true;
    fetchPosts(false);
}


// applyFilters applies a given filter to the posts.
// It resets pagination and category, sets the new filter, and fetches posts.
function applyFilters(filter) {
    currentFilter = filter || '';
    currentCategory = '';
    currentPage = 1;
    hasMorePosts = true;
    fetchPostURL = 'api/protected/api/posts';
    fetchPosts(false);
}

// submitComment handles the submission of a new comment for a given post.
// It validates and sanitizes the comment content, checks user authentication,
// sends the comment to the server, and updates the post's display.
async function submitComment(postId) {
    const commentForm = document.getElementById(`comment-form-${postId}`);
    const textarea = commentForm.querySelector('textarea');
    const content = textarea.value.trim();

    if (!content) {
        handleError('Comment cannot be empty');
        return;
    }

    // Use sanitizeHTML instead of stripAndValidateHTML
    const sanitizedContent = sanitizeHTML(content);
    if (content !== sanitizedContent) {
        handleError('HTML tags and special characters are not allowed in comments');
        textarea.value = sanitizedContent;
        return;
    }

    try {
        const authResponse = await fetch('/api/protected/api/auth/status');
        const authData = await authResponse.json();
        
        if (!authData.authenticated) {
            handleError('Please login to comment');
            return;
        }

        const response = await fetch('/api/protected/api/comments', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                post_id: postId,
                content: sanitizedContent
            })
        });

        if (!response.ok) {
            throw new Error('Failed to submit comment');
        }

        await response.json();
        textarea.value = '';
        commentForm.style.display = 'none';
        
        await updatePost(postId);
        handleSuccess('Comment posted successfully');
    } catch (error) {
        handleError('Login to post comment');
    }
}

/**
 * Fetches the latest data for a specific post and updates its
 * representation in the DOM, preserving the comment section's display state.
 * @param {number} postId - The ID of the post to update.
 */
async function updatePost(postId) {
    try {
        const response = await fetch(`/api/protected/api/posts/${postId}`);
        if (!response.ok) {
            throw new Error('Failed to fetch updated post');
        }
        
        const updatedPost = await response.json();
        
        // Find and update the specific post in the DOM
        const existingPost = document.querySelector(`.post[data-post-id="${postId}"]`);
        if (existingPost) {
            const newPost = createPostElement(updatedPost);
            
            // Preserve the comments display state
            const oldCommentsContainer = existingPost.querySelector('.comments-container');
            const newCommentsContainer = newPost.querySelector('.comments-container');
            
            if (oldCommentsContainer && oldCommentsContainer.style.display === 'block') {
                newCommentsContainer.style.display = 'block';
            }
            
            // Update the comment count in the button
            const commentBtn = newPost.querySelector('.comment-btn');
            if (commentBtn) {
                commentBtn.innerHTML = `💬 Comments (${(updatedPost.comments || []).length})`;
            }
            
            existingPost.replaceWith(newPost);
        }
    } catch (error) {
        // Error updating post - fail silently
    }
}

// applyCategoryFilter sets the current category filter for posts.
// It resets other filters and pagination, then fetches posts for the selected category.
function applyCategoryFilter(categoryId) {
    currentCategory = categoryId === '' ? '' : `category-${categoryId}`;
    currentFilter = '';
    currentPage = 1;
    hasMorePosts = true;
    fetchPostURL = 'api/protected/api/posts';
    fetchPosts(false);
}
