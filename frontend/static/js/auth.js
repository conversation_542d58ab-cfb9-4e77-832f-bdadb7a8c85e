
/**
 * @type {boolean} isLoginMode - A flag indicating whether the authentication form is currently in login mode (true) or registration mode (false).
 */
let isLoginMode = true;


/**
 * Validates the given nickname against a strict alphanumeric pattern.
 * It ensures the nickname contains only letters, numbers, and underscores, and has a length between 3 and 32 characters.
 * @param {string} nickname - The nickname to validate.
 * @returns {boolean} - True if the nickname is valid, false otherwise.
 */
function validateNickname(nickname) {
  
  const nicknameRegex = /^[0-9a-zA-Z_]+$/;
  return (
    nicknameRegex.test(nickname) &&
    nickname.length >= 3 &&
    nickname.length <= 32
  );
}


/**
 * Validates the given name against a pattern allowing letters and specific symbols.
 * It ensures the name contains only letters, underscores, and certain special characters, and has a length between 3 and 32 characters.
 * @param {string} name - The name to validate.
 * @returns {boolean} - True if the name is valid, false otherwise.
 */
function validateName(name) {
  const nameRegex = /^[a-zA-Z_<>!@#$%^&*()`~]+$/;
  return nameRegex.test(name) && name.length >= 3 && name.length <= 32;
}


/**
 * Validates the given email against a standard email pattern.
 * It ensures the email follows a typical format (e.g., <EMAIL>) and has a maximum length of 254 characters.
 * @param {string} email - The email to validate.
 * @returns {boolean} - True if the email is valid, false otherwise.
 */
function validateEmail(email) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Sets up real-time input sanitization and validation feedback for various input fields.
 * It attaches event listeners to input fields to sanitize content, validate input on blur, and display error messages.
 */
function setupInputSanitization() {
  
  const inputConfigs = {
    nickname: {
      sanitize: true,
      validate: validateNickname,
      errorMsg:
        "Nickname can only contain letters, numbers, and underscores (3 - 32 characters)",
    },
    firstName: {
      sanitize: true,
      validate: validateName,
      errorMsg: "First name can only contain letters (3- 32 characters)",
    },
    lastName: {
      sanitize: true,
      validate: validateName,
      errorMsg: "Last name can only contain letters (3-32 characters)",
    },
    email: {
      sanitize: true,
      validate: validateEmail,
      errorMsg: "Please enter a valid email address",
    },
    loginIdentifier: {
      sanitize: true,
      validate: validateLoginIdentifier,
      errorMsg: "Please enter a valid email or nickname",
    },
  };

  Object.keys(inputConfigs).forEach((inputId) => {
    const input = document.getElementById(inputId);
    const config = inputConfigs[inputId];

    if (input) {
      // Create or get error message element
      let errorElement = document.getElementById(`${inputId}-error`);
      if (!errorElement) {
        errorElement = document.createElement("div");
        errorElement.id = `${inputId}-error`;
        errorElement.className = "field-error";
        errorElement.style.color = "#ff4d4d";
        errorElement.style.fontSize = "12px";
        errorElement.style.marginTop = "4px";
        errorElement.style.display = "none";
        input.parentNode.appendChild(errorElement);
      }

      // Clear validation styling on focus
      input.addEventListener("focus", function (e) {
        hideFieldError(inputId);
      });

      // Final validation on blur
      input.addEventListener("blur", function (e) {
        const value = e.target.value.trim();
        if (value.length > 0 && !config.validate(value)) {
          showFieldError(inputId, config.errorMsg);
          e.target.classList.add("error");
        }
      });

      // Prevent paste of dangerous content
      input.addEventListener("paste", function (e) {
        setTimeout(() => {
          if (config.sanitize) {
            const cursorPos = e.target.selectionStart;
            const originalLength = e.target.value.length;
            const sanitized = e.target.value;

            if (e.target.value !== sanitized) {
              e.target.value = sanitized;
              const lengthDiff = originalLength - sanitized.length;
              const newCursorPos = Math.max(0, cursorPos - lengthDiff);
              e.target.setSelectionRange(newCursorPos, newCursorPos);
            }
          }

          // Trigger validation after paste
          input.dispatchEvent(new Event("input"));
        }, 0);
      });
    }
  });

  // Add real-time password validation
  const passwordInput = document.getElementById("password");
  if (passwordInput) {
    passwordInput.addEventListener("input", function(e) {
      checkPasswordStrength(e.target.value);
    });
  }
}

/**
 * Removes potentially dangerous characters from the input string.
 * It specifically targets characters like <, >, !, @, #, $, %, ^, &, *, (, ), `, and ~.
 * @param {string} input - The string to sanitize.
 * @returns {string} - The sanitized string.
 */
function sanitizeInput(input) {
  const sanitized = input.replace(/[<>!@#$%^&*()`~]/g, "");
  return sanitized;
}

/**
 * Displays an error message for a specific input field.
 * It takes the field's ID and the error message as input, then updates the corresponding error element's text content and makes it visible.
 * @param {string} fieldId - The ID of the input field.
 * @param {string} message - The error message to display.
 */
function showFieldError(fieldId, message) {
  const errorElement = document.getElementById(`${fieldId}-error`);
  if (errorElement) {
    errorElement.textContent = message;
    errorElement.style.display = "block";
  }
}

/**
 * Hides the error message for a specific input field.
 * It takes the field's ID as input and sets the display style of the corresponding error element to 'none'.
 * @param {string} fieldId - The ID of the input field.
 */
function hideFieldError(fieldId) {
  const errorElement = document.getElementById(`${fieldId}-error`);
  if (errorElement) {
    errorElement.style.display = "none";
  }
}

/**
 * Validates a login identifier, determining if it's an email or a nickname.
 * It checks if the identifier contains an '@' symbol to decide whether to use email validation or nickname validation.
 * @param {string} identifier - The login identifier (email or nickname) to validate.
 * @returns {boolean} - True if the identifier is valid, false otherwise.
 */
function validateLoginIdentifier(identifier) {
  if (identifier.includes("@")) {
    return validateEmail(identifier);
  } else {
    return validateNickname(identifier);
  }
}

/**
 * Performs comprehensive validation on the authentication form fields upon submission.
 * It checks for empty fields, validates input formats (email, nickname, password), and displays appropriate error messages.
 * @returns {boolean} - True if all form fields are valid, false otherwise.
 */
function validateForm() {
  const messageDiv = document.getElementById("authMessage");
  let hasErrors = false;
  let firstErrorField = null;

  
  document
    .querySelectorAll(".field-error")
    .forEach((error) => (error.style.display = "none"));
  document
    .querySelectorAll("input")
    .forEach((input) => input.classList.remove("error"));

  if (isLoginMode) {
    
    const loginIdentifier = document.getElementById("loginIdentifier").value;

    const password = document.getElementById("password").value;

    if (!loginIdentifier) {
      showFieldError("loginIdentifier", "Please enter your email or nickname");
      document.getElementById("loginIdentifier").classList.add("error");
      hasErrors = true;
      if (!firstErrorField) firstErrorField = "loginIdentifier";
    } else if (!validateLoginIdentifier(loginIdentifier)) {
      showFieldError(
        "loginIdentifier",
        "Please enter a valid email or nickname"
      );
      document.getElementById("loginIdentifier").classList.add("error");
      hasErrors = true;
      if (!firstErrorField) firstErrorField = "loginIdentifier";
    }

    if (!password) {
      showFieldError("password", "Please enter your password");
      document.getElementById("password").classList.add("error");
      hasErrors = true;
      if (!firstErrorField) firstErrorField = "password";
    }
  } else {
    
    const fields = {
      nickname: {
        value: document.getElementById("nickname").value,
        validator: validateNickname,
        message:
          "Nickname can only contain letters, numbers, and underscores (3-20 characters)",
      },
      firstName: {
        value: document.getElementById("firstName").value,
        validator: validateName,
        message: "First name can only contain letters (2-32 characters)",
      },
      lastName: {
        value: document.getElementById("lastName").value,
        validator: validateName,
        message:
          "Last name can only contain letters and spaces (2-50 characters)",
      },
      email: {
        value: document.getElementById("email").value,
        validator: validateEmail,
        message: "Please enter a valid email address",
      },
      age: {
        value: parseInt(document.getElementById("age").value),
        validator: validateAge,
        message: "Age must be between 13 and 200 years",
      },
      gender: {
        value: document.getElementById("gender").value,
        validator: (gender) =>
          ["male", "female", "other", "prefer-not-to-say"].includes(
            gender.toLowerCase()
          ),
        message: "Please select a gender",
      },
      password: {
        value: document.getElementById("password").value,
        validator: validatePassword,
        message: "Password must meet all requirements",
      },
    };

    Object.keys(fields).forEach((fieldId) => {
      const field = fields[fieldId];
      const inputElement = document.getElementById(fieldId);

      const fieldValue = typeof field.value === 'string' ? field.value.trim() : field.value;


      if (!fieldValue || fieldValue === "") {
        showFieldError(
          fieldId,
          `Please enter ${
            fieldId === "firstName"
              ? "first name"
              : fieldId === "lastName"
              ? "last name"
              : fieldId
          }`
        );
        inputElement.classList.add("error");
        hasErrors = true;
        if (!firstErrorField) firstErrorField = fieldId;
      } else if (!field.validator(field.value)) {
        showFieldError(fieldId, field.message);
        inputElement.classList.add("error");
        hasErrors = true;
        if (!firstErrorField) firstErrorField = fieldId;
      }
    });
  }

  if (hasErrors) {
    // Focus on the first field with an error
    if (firstErrorField) {
      const firstErrorElement = document.getElementById(firstErrorField);
      firstErrorElement.focus();
    }

    messageDiv.textContent = "Please correct the highlighted fields";
    messageDiv.style.display = "block";
    messageDiv.className = "auth-message error";
    return false;
  }

  messageDiv.style.display = "none";
  return true;
}

/**
 * Validates the given password based on its length.
 * It ensures the password is between 6 and 60 characters long.
 * @param {string} password - The password to validate.
 * @returns {boolean} - True if the password is valid, false otherwise.
 */
function validatePassword(password) {
  if (password.length < 8) return false;
  if (!/\d/.test(password)) return false;
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return false;
  if (!/[A-Z]/.test(password)) return false;

  // Check for common weak passwords
  const commonPasswords = ["password", "123456789", "qwerty123", "password123"];
  if (commonPasswords.some((common) => password.toLowerCase().includes(common)))
    return false;

  return true;
}

function validateAge(age){
  if (isNaN(age)||age < 13 || age > 200) {
    return false
  } 
  return true
}

let Initialized = false;
// Authentication check function
async function checkAuth() {
  try {
    const response = await fetch("/api/protected/api/auth/status");
    const data = await response.json();
    const authButtons = document.getElementById("auth-buttons");
    const userFilters = document.getElementById("userFilters");

    if (data.authenticated) {
      if (!Initialized) {
        Initialized = true;
        fetchAndDisplayOnlineUsers();
        setInterval(fetchAndDisplayOnlineUsers, 5000);
        resetPosts();
        setupInfiniteScroll();
        loadFilterCategories();
        await initInbox();
        startUnreadCheck();
      }
      // Sanitize the nickname before displaying
      const sanitizedNickname = data.nickname || "User";
      authButtons.innerHTML = `
                <div class="auth-status">
                    <span class="welcome-text">Welcome, ${sanitizedNickname}</span>
                    <button onclick="logout()" class="auth-btn logout-btn">Logout</button>
                </div>
            `;
      userFilters.style.display = "flex";
    } else {
      clearInterval(fetchAndDisplayOnlineUsers);
    
      userFilters.style.display = "none";
      openAuthModal("login");
    }
  } catch (error) {
    const authButtons = document.getElementById("auth-buttons");
    authButtons.innerHTML = `
            <button onclick="openAuthModal('login')" class="auth-btn">Login</button>
            <button onclick="openAuthModal('register')" class="auth-btn">Register</button>
        `;
    document.getElementById("userFilters").style.display = "none";
  }
}
let authInterval;
function startAuthCheck() {
  authInterval = setInterval(checkAuth, 5000);
}
function stopAuthCheck() {
  if (authInterval) {
    clearInterval(authInterval);
  }
}

// Enhanced authentication handler
async function handleAuth(event) {
  event.preventDefault();
  event.stopPropagation();

  if (!validateForm()) {
    return;
  }

  const messageDiv = document.getElementById("authMessage");
  messageDiv.style.display = "none";

  const password = document.getElementById("password").value;
  let requestBody;

  if (isLoginMode) {
    const loginIdentifier = document.getElementById("loginIdentifier").value;

    requestBody = {
      loginIdentifier,
      password,
    };
  } else {
    // Sanitize all inputs before sending
    requestBody = {
      nickname: sanitizeInput(document.getElementById("nickname").value),
      firstName: sanitizeInput(document.getElementById("firstName").value),
      lastName: sanitizeInput(document.getElementById("lastName").value),
      email: document.getElementById("email").value,
      age: parseInt(document.getElementById("age").value),
      gender: sanitizeInput(document.getElementById("gender").value),
      password,
    };
  }

  try {
    const response = await fetch(`/api/${isLoginMode ? "login" : "register"}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      if (isLoginMode) {
        showAuthSuccess("Login successful");
        startAuthStatusCheck();
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        showAuthSuccess("Registration successful! Please login.");
        setTimeout(() => {
          isLoginMode = true;
          openAuthModal("login", "Registration successful! Please login.");
        }, 1000);
      }
    } else {
      // Fix: Use the actual error message from the server instead of escaping it
      const errorMessage = data.message || "An error occurred";
      showAuthError(errorMessage);
      console.error("Auth error:", errorMessage);
    }
  } catch (error) {
    // Fix: Log the actual error and don't show success message
    console.error("Auth error:", error);
    showAuthError("An error occurred. Please try again later.");
  }
}

// Initialize input sanitization when the page loads
/**
 * Initializes the authentication module when the DOM is fully loaded.
 * Sets up the authentication modal, attaches event listeners to login/register/logout buttons,
 * and checks for URL hashes to automatically open the modal in a specific mode.
 * Also starts a periodic authentication status check.
 */
document.addEventListener("DOMContentLoaded", () => {
  startAuthCheck();
  setupInputSanitization();
});

function showAuthError(message) {
  const messageDiv = document.getElementById("authMessage");
  messageDiv.textContent = message;
  messageDiv.style.display = "block";
  messageDiv.className = "auth-message error";
}

function showAuthSuccess(message) {
  const messageDiv = document.getElementById("authMessage");
  messageDiv.textContent = message;
  messageDiv.style.display = "block";
  messageDiv.className = "auth-message success";
}

function openAuthModal(mode, message = "") {
  isLoginMode = mode === "login";
  const modalTitle = document.getElementById("modalTitle");
  const messageDiv = document.getElementById("authMessage");
  const strengthContainer = document.getElementById(
    "password-strength-container"
  );
  const requirementsDiv = document.getElementById("password-requirements");
  const loginFields = document.getElementById("loginFields");
  const registerFields = document.getElementById("registerFields");

  modalTitle.textContent = isLoginMode ? "Login" : "Register";

  if (message) {
    showAuthSuccess(message);
  } else {
    messageDiv.style.display = "none";
  }

  strengthContainer.style.display = isLoginMode ? "none" : "block";
  requirementsDiv.style.display = isLoginMode ? "none" : "block";
  loginFields.style.display = isLoginMode ? "block" : "none";
  registerFields.style.display = isLoginMode ? "none" : "block";

  document.getElementById("authModal").classList.add("active");
  document.querySelector(".modal-switch").textContent = isLoginMode
    ? "Register Instead"
    : "Login Instead";
  document.querySelector(".modal-submit").textContent = isLoginMode
    ? "Login"
    : "Register";

  if (!isLoginMode) {
    checkPasswordStrength("");
    stopAuthCheck();
  }

  // Clear any existing field errors and styling
  document
    .querySelectorAll(".field-error")
    .forEach((error) => (error.style.display = "none"));
  document.querySelectorAll("input").forEach((input) => {
    input.classList.remove("error", "valid");
  });

  // Setup input sanitization for the modal (call this after the modal is open)
  setTimeout(() => {
    setupInputSanitization();
  }, 100);
}

function closeAuthModal() {
  document.getElementById("authModal").classList.remove("active");
  document.getElementById("authForm").reset();
}

function toggleAuthMode() {
  isLoginMode = !isLoginMode;
  openAuthModal(isLoginMode ? "login" : "register");
}

function startAuthStatusCheck() {
  setInterval(async () => {
    try {
      const response = await fetch("/api/protected/api/auth/status");
      if (!response.ok) {
        handleError("Your session has ended. Please login again.");
        window.location.href = "/";
      }
    } catch (error) {
      console.error("Auth check failed:", error);
    }
  }, 30000);
}

async function logout() {
  try {
    const response = await fetch("/api/protected/api/logout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    });

    if (!response.ok) {
      if (response.status === 405) {
        handleError("Invalid request method");
        return;
      }
      throw new Error("Logout failed");
    }

    window.location.reload();
  } catch (error) {
    console.error("Error logging out:", error);
    handleError("Failed to logout. Please try again.");
  }
}

function checkPasswordStrength(password) {
  const strengthMeter = document.getElementById("password-strength");
  const strengthText = document.getElementById("strength-text");

  const lengthCheck = document.getElementById("length-check");
  const numberCheck = document.getElementById("number-check");
  const specialCheck = document.getElementById("special-check");
  const uppercaseCheck = document.getElementById("uppercase-check");

  if (!password) {
    strengthMeter.style.width = "0%";
    strengthMeter.style.backgroundColor = "#e0e0e0";
    strengthText.textContent = "";

    [lengthCheck, numberCheck, specialCheck, uppercaseCheck].forEach(
      (check) => {
        if (check) check.classList.remove("valid");
      }
    );
    return;
  }

  let strength = 0;

  // Check length requirement
  if (password.length >= 8) {
    strength += 25;
    if (lengthCheck) lengthCheck.classList.add("valid");
  } else {
    if (lengthCheck) lengthCheck.classList.remove("valid");
  }

  // Check number requirement
  if (/\d/.test(password)) {
    strength += 25;
    if (numberCheck) numberCheck.classList.add("valid");
  } else {
    if (numberCheck) numberCheck.classList.remove("valid");
  }

  // Check special character requirement
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    strength += 25;
    if (specialCheck) specialCheck.classList.add("valid");
  } else {
    if (specialCheck) specialCheck.classList.remove("valid");
  }

  // Check uppercase requirement
  if (/[A-Z]/.test(password)) {
    strength += 25;
    if (uppercaseCheck) uppercaseCheck.classList.add("valid");
  } else {
    if (uppercaseCheck) uppercaseCheck.classList.remove("valid");
  }

  // Update strength meter
  strengthMeter.style.width = `${strength}%`;

  if (strength <= 25) {
    strengthMeter.style.backgroundColor = "#ff4d4d";
    strengthText.textContent = "Weak";
  } else if (strength <= 50) {
    strengthMeter.style.backgroundColor = "#ffd700";
    strengthText.textContent = "Fair";
  } else if (strength <= 75) {
    strengthMeter.style.backgroundColor = "#2ecc71";
    strengthText.textContent = "Good";
  } else {
    strengthMeter.style.backgroundColor = "#27ae60";
    strengthText.textContent = "Strong";
  }
}
