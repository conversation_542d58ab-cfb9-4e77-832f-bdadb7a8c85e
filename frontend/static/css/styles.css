/* ==================== */

:root {
    --primary-color: #4CAF50;
    --primary-hover: #45a049;
    --bg-dark: #1a1a1a;
    --bg-card: #242424;
    --bg-elevated: #2a2a2a;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --border-color: #333;
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
#typing-indicator {
    display: none; 
    overflow: hidden;
    white-space: nowrap;
    border-right: .15em solid orange; 
    font-family: 'Monospace', monospace; 
    font-size: 2em;
    width: 0; 
    animation: 
        typing 3.5s steps(40, end) forwards,
        blink-caret .75s step-end infinite;
}


@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-primary);
    line-height: 1.4;
    padding-top: 70px;
}


header {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

nav {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.nav-left, .nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: auto;
}

.logo {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
}

.search-bar {
    position: relative;
    width: 100%;
}

.search-bar input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.95rem;
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-btn {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--bg-dark);
}


main {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
}


.sidebar-left, .sidebar-right {
    position: sticky;
    top: 90px;
    height: calc(100vh - 90px);
    overflow-y: auto;
}

.sidebar-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.categories-section, .user-filters, .trending-section, .online-users {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.categories-section h3, .user-filters h3, .trending-section h3, .online-users h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}


.filter-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    text-align: left;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filter-btn:hover {
    background-color: var(--bg-elevated);
    border-color: var(--primary-color);
}

.filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.filter-btn i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-list .filter-btn {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    width: 100%;
    text-align: left;
}

.category-list .filter-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-dark);
}

.category-list .filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.category-filter-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.category-filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.category-filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.category-filter-btn i {
    font-size: 16px;
}

/* ==================== */
/* Main Content Area */
/* ==================== */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.sort-options select {
    padding: 0.5rem 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
}

/* ==================== */
/* Posts */
/* ==================== */
.post {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.post:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.post-title {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.post-meta-info {
    color: var(--text-muted);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.post-content p {
    color: var(--text-primary);
    white-space: pre-wrap;
    word-break: break-word;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* ==================== */
/* Categories */
/* ==================== */
.categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-tag {
    background-color: var(--bg-elevated);
    color: var(--text-secondary);
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    border: 1px solid var(--primary-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.category-tag:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
    background-color: rgba(76, 175, 80, 0.1);
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
    }
}

.category-tag:hover {
    animation: glow 1.5s ease-in-out infinite alternate;
}

/* ==================== */
/* Post Actions */
/* ==================== */
.post-actions {
    display: flex;
    gap: 1rem;
}

.post-actions button {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.post-actions button:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-dark);
}

.like-btn:hover {
    color: #4CAF50;
}

.dislike-btn:hover {
    color: #f44336;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--border-radius);
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: var(--bg-elevated);
}

.action-btn i {
    font-size: 1.1rem;
}

/* ==================== */
/* Comments Section */
/* ==================== */
.comments-container {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.comment {
    background-color: var(--bg-elevated);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.comment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
}

.comment-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.comment-form {
    margin-top: 1rem;
}

.comment-form textarea {
    width: 100%;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: var(--border-radius);
    resize: vertical;
    min-height: 100px;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.comment-form textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.add-comment-btn, .comment-form button {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.95rem;
    width: auto;
}

.add-comment-btn:hover, .comment-form button:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--bg-dark);
}

/* ==================== */
/* Auth Buttons */
/* ==================== */
.auth-btn {
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 0.95rem;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.auth-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.logout-btn {
    border: 1px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* ==================== */
/* Athentification Message Errors*/
/* ==================== */
.auth-message {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
}

.auth-message.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.auth-message.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid #f44336;
}

/* ==================== */
/* Create Post Button */
/* ==================== */
.create-post-btn {
    position: sticky;
    top: 0;
    z-index: 2;
    margin-bottom: 1rem;
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    border: none;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.create-post-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.create-post-btn i {
    font-size: 1.1rem;
}

/* ==================== */
/* User Filters */
/* ==================== */
.user-filters {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.user-filters#userFilters {
    display: flex !important;
    flex-direction: column !important;
}

.user-filters .filter-btn {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    font-size: 0.95rem;
}

.user-filters .filter-btn i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.user-filters .filter-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-dark);
}

.user-filters .filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* ==================== */
/* Scrollbar Styling */
/* ==================== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ==================== */
/* Responsive Design */
/* ==================== */
@media (min-width: 1131px) {
    main {
        gap: 1rem;
    }
    .main-content {
        min-width: 250px;
    }
    .sidebar-toggle {
        display: none;
    }
}

@media (max-width: 1130px) {

    main {
        display: block;
        padding: 1rem;
    }
    body {
        padding-top: 5vh;
        background: #121212; 
        color: #e0e0e0;
    }

    /* Sidebar styles */
    .sidebar-left,
    .sidebar-right {
        position: fixed;
        top: 0;
        height: 100%;
        width: 260px;
        background: #1a1a1a;
        z-index: 1000;
        transition: transform 0.3s ease-in-out;
        overflow-y: auto;
        padding: 1rem;
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.2);
        border-left: 1px solid #4CAF506E;
    }
    .sidebar-left {
        left: 0;
        transform: translateX(-100%);
    }
    .sidebar-right {
        right: 0;
        transform: translateX(100%);
    }
    .sidebar-left.active,
    .sidebar-right.active {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #4caf4f8f;
        color: white;
        font-size: 1.5rem;
        border: none;
        cursor: pointer;
        box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        transition: all 0.3s ease-in-out;
        z-index: 1001;
    }
    .sidebar-toggle:hover {
        background: #388E3C;
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.7);
    }

    .toggle-left {
        left: 1.5rem;
        bottom: 2rem;
    }
    .toggle-right {
        right: 1.5rem;
        bottom: 2rem;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
    }
    .main-content {
        width: 100%;
    }
    .post-footer {
        flex-direction: column;
        gap: 1rem;
    }
}
@media (max-width: 768px) {
    .nav-right {
        gap: 0.5rem;
        align-items: flex-end; 
    }
}
@media (max-width: 480px) {
    .nav-right button {
        padding: 0.6rem 0.6rem; 
        font-size: 0.9rem; 
    }
}

/* ==================== */
/* Toast Notifications */
/* ==================== */
#toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    min-width: 300px;
    padding: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-card);
    color: var(--text-primary);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slide-in 0.3s ease-out;
    border: 1px solid var(--border-color);
    border-left: 4px solid var(--primary-color);
}

.toast i {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.toast.error {
    border-left: 4px solid #f44336;
}

.toast.success {
    border-left: 4px solid var(--primary-color);
}

.toast span {
    flex: 1;
}

.toast button {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0 0.5rem;
    transition: var(--transition);
}

.toast button:hover {
    color: var(--text-primary);
}

.toast.fade-out {
    animation: slide-out 0.3s ease-out forwards;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* ==================== */
/* Modal Styles */
/* ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.75);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    position: relative;
    transform: translateY(-20px);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.modal-footer button {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
}

.modal-submit {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.modal-submit:hover {
    background-color: var(--primary-hover);
}

.modal-switch {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.modal-switch:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* ==================== */
/* Password Strength Meter */
/* ==================== */
.strength-container {
    margin-top: 5px;
    width: 100%;
}

.strength-meter-container {
    width: 100%;
    height: 4px;
    background-color: var(--bg-elevated);
    border-radius: 2px;
    margin-top: 5px;
}

.strength-meter {
    height: 100%;
    width: 0;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.strength-text {
    font-size: 12px;
    margin-top: 4px;
    display: block;
    color: var(--text-secondary);
}

/* ==================== */
/* Textarea Styling in Modal */
/* ==================== */
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.95rem;
    min-height: 150px;
    resize: vertical;
    transition: var(--transition);
}

.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* ==================== */
/* Category Checkboxes Styling */
/* ==================== */
.category-checkboxes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.category-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.category-checkbox:hover {
    border-color: var(--primary-color);
}

.category-checkbox input {
    width: auto;
    margin: 0;
}

.category-checkbox span {
    color: var(--text-primary);
    font-size: 0.9rem;
}

#createPostModal .modal {
    max-width: 600px;
}

/* ==================== */
/* Auth Status Styles */
/* ==================== */
.auth-status {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    width: 100%;
}

.welcome-text {
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-right: auto;
}

/* ==================== */
/* Error Message */
/* ==================== */
.error-message {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid #f44336;
    border-radius: var(--border-radius);
    padding: 10px;
    margin: 10px 0;
}

.error-message .error-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #f44336;
}

.error-message .fas {
    font-size: 16px;
}

.error-message span {
    font-size: 14px;
    color: #f44336;
}

/* ==================== */
/* Load More Button */
/* ==================== */
#load-more-trigger {
    text-align: center;
    padding: 1rem;
    margin-top: 1rem;
}

.load-more-btn {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.load-more-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid var(--bg-elevated);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ==================== */
/* Read More Section */
/* ==================== */
.read-more-section {
    margin-top: 0.5rem;
}

.read-more-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    transition: var(--transition);
}

.read-more-btn:hover {
    text-decoration: underline;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: var(--border-radius);
}

/* ==================== */
/* Password Requirements Section */
/* ==================== */
.password-requirements {
    background-color: var(--bg-elevated);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.password-requirements h4 {
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.password-requirements li i {
    font-size: 0.6rem;
    color: var(--text-muted);
}

.password-requirements li.valid {
    color: var(--primary-color);
}

.password-requirements li.valid i {
    color: var(--primary-color);
}

/* ==================== */
/* Stats Section */
/* ==================== */
.stats-container {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    padding: 0.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1rem;
    background-color: var(--bg-elevated);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.stat-item.likes::before {
    background-color: #ffd700;
}

.stat-item.comments::before {
    background-color: var(--primary-color);
}

.stat-item.posts::before {
    background-color: #f3f021;
}

.stat-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    transition: all 0.3s ease;
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    opacity: 0.2;
    z-index: -1;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1);
}

.stat-icon.likes {
    background-color: rgba(255, 215, 0, 0.1);
    color: #ffd700;
}

.stat-icon.comments {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
}

.stat-icon.posts {
    background-color: rgba(100, 243, 33, 0.1);
    color: #ffd700;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    flex: 1;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-value::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: pulse 2s infinite;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    letter-spacing: 0.5px;
}

.stat-category h4 {
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 1rem;
    justify-content: center;
    font-weight: 300;
}

.category-name {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    display: inline;
    align-items: center;
    gap: 0.5rem;
}

.category-name::before {
    content: '#';
    color: var(--primary-color);
}

.category-count {
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 500;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* ==================== */
/* Social Media Section */
/* ==================== */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1.2rem;
    padding: 1rem 0;
}

.social-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link i {
    font-size: 1.2rem;
}

.social-link.github:hover {
    background-color: #24292e;
    color: white;
    transform: translateY(-3px);
}

.social-link.twitter:hover {
    background-color: #1DA1F2;
    color: white;
    transform: translateY(-3px);
}

.social-link.linkedin:hover {
    background-color: #0077b5;
    color: white;
    transform: translateY(-3px);
}

.social-link.discord:hover {
    background-color: #7289DA;
    color: white;
    transform: translateY(-3px);
}

/* ==================== */
/* Post Validation Error Message */
/* ==================== */
.error-message {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid #f44336;
    border-radius: var(--border-radius);
    padding: 10px;
    margin: 10px 0;
}

.error-message .error-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #f44336;
}

.error-message .fas {
    font-size: 16px;
}

.error-message span {
    font-size: 14px;
    color: #f44336;
}
.badge {
    /* position: absolute; */
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    min-width: 16px;
    text-align: center;
    display: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}


/* Chat Container */
.chat-section {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background-color: var(--bg-elevated);
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

.chat-msg {
    max-width: 70%;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    /* position: relative; */
}

.chat-msg.sent {
    align-self: flex-end;
    display: flex;
    background-color: var(--primary-color);
    color: black;
    border: 1px solid white;
    overflow-wrap:anywhere;
    max-width: 90%; 
}

.chat-msg.received {
    align-self: flex-start;
    background-color: var(--bg-card);
    border: 2px solid var(--primary-color);
}

.msg-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    gap: 1rem;
}
.msg-content {
    /* display: flex; */
    /* flex-direction:row; */
    gap: 0.25rem;
}

.msg-time {
    font-size: 0.8rem;
    opacity: 0.8;
    align-self: flex-end;
}
.msg-text{
    overflow: auto;
}
.msg-sender{
    align-self: flex-start;
}

/* Message Input Form */
.send-msg-form {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.send-msg-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid white;
    border-radius: var(--border-radius);
    color: var(--text-primary);
}

.send-msg-form button {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.send-msg-form button:hover {
    background-color: var(--primary-hover);
}

/* Back Button */
.back-btn {
    padding: 0.5rem 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.back-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Conversation List */
.conversation-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.conversation-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.conversation-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.conv-details {
    flex: 1;
}

.conv-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.conv-preview {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.conv-time {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

#inbox-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
}


#inbox-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}
#inbox-btn:hover i {
    transform: scale(1.1);
}

#inbox-btn i {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
}

#inbox-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

#inbox-btn.active i {
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    #inbox-btn span:not(.badge) {
        display: none;
    }
    
    #inbox-btn {
        padding: 0.75rem;
    }
    
    #inbox-btn i {
        margin: 0;
    }
}

/* #msg-input::placeholder {
    color: white;
    opacity: 1;
  }

  #msg-input::-webkit-input-placeholder {
    color: white;
  }
  #msg-input::-moz-placeholder {
    color: white;
    opacity: 1;
  }
  #msg-input:-ms-input-placeholder {
    color: white;
  }
  #msg-input::-ms-input-placeholder {
    color: white;
  } */