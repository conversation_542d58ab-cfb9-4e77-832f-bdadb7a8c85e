<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Forum</title>
	<link rel="stylesheet" href="/static/css/styles.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico"></head>
<body>
	<header>
		<nav>
			<div class="nav-left">
				<a href="/" class="logo">
					<i class="fas fa-comments"></i>
					Forum
				</a>
			</div>

			<div class="nav-right">

				<button id="inbox-btn">
					<i class="fas fa-envelope"></i>
					Inbox
					<span id="unread-badge" class="badge"></span>
				</button>
				<div id="auth-buttons"></div>
			</div>
		</nav>
	</header>

	<div class="sidebar-toggle toggle-left" id="toggleLeft">
		<i class="fas fa-bars"></i>
	</div>
	<div class="sidebar-toggle toggle-right" id="toggleRight">
		<i class="fas fa-ellipsis-v"></i>
	</div>
	<div class="sidebar-overlay" id="overlay"></div>

	<main>
		<aside class="sidebar-left">
			<div class="user-filters" id="userFilters">
				<h3>Filters</h3>
				<button onclick="applyFilters('my-posts')" class="filter-btn">
					<i class="fas fa-user"></i> My Posts
				</button>
				<button onclick="applyFilters('liked-posts')" class="filter-btn">
					<i class="fas fa-heart"></i> Liked Posts
				</button>
			</div>
			<div class="categories-section">
				<h3>Categories</h3>
				<div id="categoryFilter" class="category-list">
					<!-- Categories will be loaded here as buttons -->
				</div>
			</div>

		</aside>

		<section class="main-content" id="main-content">
			<div class="content-header">
				<h2>Recent Posts</h2>
			</div>
			<div id="posts-list">
				<!-- Posts -->
			</div>
		</section>

		<aside class="sidebar-right">
			<button onclick="openCreatePostModal()" class="create-post-btn">
				<i class="fas fa-plus"></i> Create Post
			</button>
			<div class="trending-section">
				<h3>Quick Forum Stats</h3>
				<!-- Trending -->
			</div>
			
		</aside>
	</main>

	<div id="toast-container"></div>

	<div id="authModal" class="modal-overlay">
		<div class="modal">
			<div class="modal-header">
				<h3 id="modalTitle">Login</h3>
			</div>
			<div id="authMessage" class="auth-message" style="display: none;"></div>
			<div class="modal-body"closeAuth>
				<form id="authForm" onsubmit="handleAuth(event)">
					
					<div id="loginFields">
						<div class="form-group">
							<label for="loginIdentifier">Email or Nickname</label>
							<input type="text" id="loginIdentifier" required>
						</div>
					</div>


					<div id="registerFields" style="display: none;">
						
						<div class="form-group">
							<label for="nickname">Nickname</label>
							<input type="text" id="nickname" required>
						</div>
						<div class="form-group">
							<label for="firstName">First Name</label>
							<input type="text" id="firstName" required>
						</div>
						<div class="form-group">
							<label for="lastName">Last Name</label>
							<input type="text" id="lastName" required>
						</div>
						<div class="form-group">
							<label for="email">Email</label>
							<input type="email" id="email" required>
						</div>
						<div class="form-group">
							<label for="age">Age</label>
							<input type="number" id="age" min="13" required>
						</div>
						<div class="form-group">
							<label for="gender">Gender</label>
							<select id="gender" required>
								<option value="">Select Gender</option>
								<option value="male">Male</option>
								<option value="female">Female</option>
								<option value="other">Other</option>
							</select>
						</div>
					</div>


					<div class="form-group">
						<label for="password">Password</label>
						<input type="password" id="password"  required>
						<div id="password-strength-container" class="strength-container" style="display: none;">
							<div class="strength-meter-container">
								<div id="password-strength" class="strength-meter"></div>
							</div>
							<span id="strength-text" class="strength-text"></span>
						</div>
					</div>
				</form>
			</div>
			<div id="password-requirements" class="password-requirements" style="display: none;">
				<h4>Requirements:</h4>
				<h4>Password</h4>
				<ul>
					<li id="length-check"><i class="fas fa-circle"></i> At least 8 characters</li>
					<li id="number-check"><i class="fas fa-circle"></i> At least 1 number</li>
					<li id="special-check"><i class="fas fa-circle"></i> At least 1 special character</li>
					<li id="uppercase-check"><i class="fas fa-circle"></i> At least 1 uppercase letter</li>
				</ul>
				<h4>Age</h4>
				<ul>
					<li id="age-check"><i class="fas fa-circle"></i> Between 13 and 200 years </li>
					<li id="special-check"><i class="fas fa-circle"></i> Must be a digit (0-9)</li>
				</ul>
			</div>
			<div class="modal-footer">
				<button class="modal-submit" onclick="handleAuth(event)">Login</button>
				<button class="modal-switch" onclick="toggleAuthMode()">Register Instead</button>
			</div>
		</div>
	</div>

	<div class="modal-overlay" id="createPostModal">
		<div class="modal">
			<div class="modal-header">
				<h3>Create New Post</h3>
				<button class="modal-close" onclick="closeCreatePostModal()">&times;</button>
			</div>
			<div class="modal-body">
				<form id="createPostForm" onsubmit="handleCreatePost(event)">
					<div class="form-group">
						<label for="postTitle">Title</label>
						<input type="text" id="postTitle" required>
					</div>
					<div class="form-group">
						<label for="postContent">Content</label>
						<textarea id="postContent" required></textarea>
					</div>
					<div class="form-group">
						<label>Categories</label>
						<div class="category-checkboxes" id="postCategories">
							<!-- Categories -->
						</div>
					</div>
					<div id="post-error-message" class="error-message" style="display: none;"></div>
				</form>
			</div>
			<div class="modal-footer">
				<button class="modal-submit" onclick="handleCreatePost(event)">Create Post</button>
				<button class="modal-switch" onclick="closeCreatePostModal()">Cancel</button>
			</div>
		</div>
	</div>
	<script src="/static/js/notifications.js"></script>
	<script src="/static/js/auth.js"></script>
	<script src="/static/js/comments.js"></script>
	<script src="/static/js/posts.js"></script>
	<!-- <script src="/static/js/filters.js"></script> -->
	<script src="/static/js/main.js"></script>
	<script src="/static/js/status.js"></script>
	<script src="/static/js/toggle.js"></script>
	<script src="/static/js/inbox.js"></script>
	<script src="/static/js/error_page.js"></script>
	<script src="/static/js/error_page.js"></script>


</body>
</html>